// Build deployment package for VUM Monitor Service
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const BUILD_DIR = './build';
const PACKAGE_NAME = 'vum-monitor-service';
const VERSION = '1.0.0';

// Core production files (ESSENTIAL)
const CORE_FILES = [
  'concurrent-monitor-service.js',
  'notification-listener.js',
  'direct-email-sender.js',
  'email-notification-listener.js',
  'check-env.js',
  'check-service-status.js',
  'package.json',
  'package-lock.json',
  '.env.example'
];

// Optional production files (include only if they exist and are needed)
const OPTIONAL_FILES = [
  'health-check.js',
  'create-monitor-table.js',
  'concurrent-manual-check.js'
];

// Combine all files to include in the deployment
const INCLUDE_FILES = [
  ...CORE_FILES,
  ...OPTIONAL_FILES.filter(file => fs.existsSync(file))
];

// Directories to create in the deployment
const INCLUDE_DIRS = ['logs', 'config'];

// Linux-specific files to include
const LINUX_FILES = ['vum-monitor.service'];

console.log(`🚀 Building VUM Monitor Service v${VERSION} deployment package...\n`);

// Clean and create build directory
if (fs.existsSync(BUILD_DIR)) {
  console.log('📁 Cleaning existing build directory...');
  fs.rmSync(BUILD_DIR, { recursive: true, force: true });
}

fs.mkdirSync(BUILD_DIR, { recursive: true });
console.log('📁 Created build directory');

// Create package directory
const packageDir = path.join(BUILD_DIR, PACKAGE_NAME);
fs.mkdirSync(packageDir, { recursive: true });

// Copy files
console.log('\n📋 Copying files...');
let copiedFiles = 0;
let skippedFiles = 0;

// Copy core and optional files
INCLUDE_FILES.forEach(file => {
  const sourcePath = path.join('.', file);
  const destPath = path.join(packageDir, file);

  if (fs.existsSync(sourcePath)) {
    // Create directory if it doesn't exist
    const destDir = path.dirname(destPath);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }

    fs.copyFileSync(sourcePath, destPath);
    console.log(`✅ Copied: ${file}`);
    copiedFiles++;
  } else {
    console.log(`⚠️  Not found: ${file}`);
    skippedFiles++;
  }
});

// Create directories
console.log('\n📂 Creating directories...');
INCLUDE_DIRS.forEach(dir => {
  const dirPath = path.join(packageDir, dir);
  fs.mkdirSync(dirPath, { recursive: true });
  console.log(`✅ ${dir}/`);
});

// Create deployment scripts
console.log('\n📝 Creating deployment scripts...');

// Create setup script for Windows
const setupScriptWindows = `@echo off
echo VUM Monitor Service - Setup Script
echo ====================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found:
node --version

echo.
echo Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Setup completed successfully!
echo.
echo Next steps:
echo 1. Copy .env.example to .env
echo 2. Edit .env with your configuration
echo 3. Run: npm run create-tables
echo 4. Run: npm start
echo.
pause
`;

fs.writeFileSync(path.join(packageDir, 'setup.bat'), setupScriptWindows);
console.log('✅ setup.bat');

// Create setup script for Linux/Mac
const setupScriptUnix = `#!/bin/bash
echo "VUM Monitor Service - Setup Script"
echo "===================================="
echo

echo "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js found: $(node --version)"

echo
echo "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "Setup completed successfully!"
echo
echo "Next steps:"
echo "1. Copy .env.example to .env"
echo "2. Edit .env with your configuration"
echo "3. Run: npm run create-tables"
echo "4. Run: npm start"
echo

# Set permissions
chown -R vummonitor:vummonitor "$INSTALL_DIR"
chmod 600 "$INSTALL_DIR/.env"

echo "\nInstallation complete!"
echo "1. Edit the configuration: nano $INSTALL_DIR/.env"
echo "2. Start the service: systemctl start vum-monitor"
echo "3. Check status: systemctl status vum-monitor"
echo "4. View logs: journalctl -u vum-monitor -f"
`;

fs.writeFileSync(path.join(packageDir, 'setup.sh'), setupScript);
fs.chmodSync(path.join(packageDir, 'setup.sh'), '755');
console.log('✅ Created setup.sh');

// Create start script for manual execution
const startScript = `#!/bin/bash
# Start VUM Monitor Service (for manual execution)

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting VUM Monitor Service..."

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install --production
fi

# Start the service
node concurrent-monitor-service.js
`;

fs.writeFileSync(path.join(packageDir, 'start.sh'), startScript);
fs.chmodSync(path.join(packageDir, 'start.sh'), '755');
console.log('✅ Created start.sh');

// Create deployment README
const deploymentReadme = `# VUM Monitor Service - Production Deployment

This is a minimal, production-ready package of the VUM Monitor Service.

## Quick Start

### Linux
1. Run \`./setup.sh\` to install dependencies
2. Verify \`.env\` file has your correct settings
3. Run \`npm run create-tables\` to set up database tables (if needed)
4. Run \`systemctl start vum-monitor\` to start the service

## What's Included

This minimal package includes only essential files:
- **Core Service**: concurrent-monitor-service.js, checker.js, database.js, logger.js
- **Notifications**: notification-listener.js
- **Configuration**: .env (your actual config), package.json
- **Setup Tools**: create-monitor-table.js, check-env.js
- **Manual Checks**: concurrent-manual-check.js (for dashboard integration)
- **Service Management**: Linux service install/uninstall scripts

## Available Commands

- \`npm start\` - Start the monitor service
- \`npm run start-notifications\` - Start notification listener
- \`npm run check-env\` - Check environment configuration
- \`npm run create-tables\` - Create database tables
- \`npm run status\` - Check service status
- \`npm run concurrent-manual-check --id=<monitor_id>\` - Manual check for dashboard

## Service Management (Linux)

- \`systemctl start vum-monitor\` - Start the service
- \`systemctl stop vum-monitor\` - Stop the service
- \`systemctl restart vum-monitor\` - Restart the service
- \`systemctl status vum-monitor\` - Check service status

## Logs

Logs are stored in the \`logs/\` directory:
- \`monitor-service.log\` - Main service logs
- \`error.log\` - Error logs only

## Support

For issues and documentation, visit: https://github.com/RayZwankhuizen/VUM-Backend
`;

fs.writeFileSync(path.join(packageDir, 'DEPLOYMENT-README.md'), deploymentReadme);
console.log('✅ DEPLOYMENT-README.md');

// Create tarball (better for Linux)
console.log('\n📦 Creating Linux deployment package...');
try {
  // Create a tarball
  execSync(`cd ${BUILD_DIR} && tar -czf ${PACKAGE_NAME}.tar.gz ${PACKAGE_NAME}`);
  console.log(`✅ Created ${PACKAGE_NAME}.tar.gz in ${BUILD_DIR}/`);
  
  // Create a checksum file
  const checksum = execSync(`cd ${BUILD_DIR} && shasum -a 256 ${PACKAGE_NAME}.tar.gz`).toString();
  fs.writeFileSync(path.join(BUILD_DIR, `${PACKAGE_NAME}.sha256`), checksum);
  console.log(`✅ Created checksum file: ${PACKAGE_NAME}.sha256`);
  
  // Create deployment instructions
  const instructions = `# VUM Monitor Service Deployment

## Installation

1. Copy the package to your server:
   \`\`\`bash
   scp ${PACKAGE_NAME}.tar.gz user@your-server:/tmp/
   \`\`\`

2. On the server, extract and install:
   \`\`\`bash
   # Extract the package
   tar -xzf /tmp/${PACKAGE_NAME}.tar.gz -C /opt/
   
   # Run the setup script (as root)
   cd /opt/${PACKAGE_NAME}
   sudo ./setup.sh
   \`\`\`

3. Configure the service:
   \`\`\`bash
   # Edit the configuration file
   sudo nano /opt/${PACKAGE_NAME}/.env
   
   # Start the service
   sudo systemctl start vum-monitor
   
   # Check status
   sudo systemctl status vum-monitor
   \`\`\`

## Health Check

Once running, you can check the service status at:
- Health: http://your-server-ip:3001/health
- Status: http://your-server-ip:3001/status

## Logs

View logs with:
\`\`\`bash
journalctl -u vum-monitor -f
\`\`\`
`;

  fs.writeFileSync(path.join(BUILD_DIR, 'DEPLOYMENT.md'), instructions);
  console.log('✅ Created DEPLOYMENT.md with deployment instructions');

  console.log(`✅ Created: ${PACKAGE_NAME}.tar.gz`);

  // Get file size
  const stats = fs.statSync(path.join(BUILD_DIR, `${PACKAGE_NAME}.tar.gz`));
  const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);

  console.log(`📊 Archive size: ${fileSizeInMB} MB`);

} catch (error) {
  console.log('⚠️  Could not create archive automatically. You can manually zip the folder.');
  console.log('Error:', error.message);
}

// Summary
console.log('\n🎉 Build completed successfully!');
console.log('\n📋 Summary:');
console.log(`   Files copied: ${copiedFiles}`);
console.log(`   Files skipped: ${skippedFiles}`);
console.log(`   Package location: ${packageDir}`);
console.log('\n📝 Next steps:');
console.log('   1. Copy the build folder to your target server');
console.log('   2. Run the setup script on the target server');
console.log('   3. Configure the .env file');
console.log('   4. Start the service');
console.log('\n✨ Ready for deployment!');
