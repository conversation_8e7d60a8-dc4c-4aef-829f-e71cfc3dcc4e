// Concurrent Monitor Service - A background process that checks monitors in parallel
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');
const express = require('express');
const cors = require('cors');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'monitor-service.log');
const CHECK_INTERVAL = 10000; // Check every 10 seconds if any monitors need to be checked
const MAX_CONCURRENT_CHECKS = process.env.MAX_CONCURRENT_CHECKS || 10; // Maximum number of concurrent checks
const HEALTH_CHECK_PORT = process.env.HEALTH_CHECK_PORT || 3001; // Port for health check server

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Store scheduled monitors
const monitorSchedules = new Map();

// Worker pool for concurrent checks
const workerPool = {
  activeWorkers: 0,
  maxWorkers: MAX_CONCURRENT_CHECKS,
  queue: [],

  // Add a task to the queue
  addTask(task) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        task,
        resolve,
        reject
      });
      this.processQueue();
    });
  },

  // Process the queue
  processQueue() {
    if (this.queue.length === 0 || this.activeWorkers >= this.maxWorkers) {
      return;
    }

    const { task, resolve, reject } = this.queue.shift();
    this.activeWorkers++;

    Promise.resolve()
      .then(() => task())
      .then(resolve)
      .catch(reject)
      .finally(() => {
        this.activeWorkers--;
        this.processQueue();
      });
  }
};

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Get system settings for degraded thresholds
async function getDegradedThresholds() {
  try {
    const { data, error } = await supabase
      .from('system_settings')
      .select('value')
      .eq('key', 'degraded_thresholds')
      .single();

    if (error) {
      log(`Error fetching degraded thresholds: ${error.message}`, 'ERROR');
      // Return default values if we can't fetch from the database
      return {
        response_time: 1000, // 1 second
        error_rate: 10, // 10%
        status_codes: [429, 503], // Rate limiting and service unavailable
        consecutive_failures: 2
      };
    }

    return data.value;
  } catch (error) {
    log(`Error in getDegradedThresholds: ${error.message}`, 'ERROR');
    // Return default values
    return {
      response_time: 1000,
      error_rate: 10,
      status_codes: [429, 503],
      consecutive_failures: 2
    };
  }
}

// Check a monitor
async function performCheck(monitor, isManualCheck = false) {
  const startTime = Date.now();
  let status = 'down'; // Default to down
  let responseTime = 0;
  let errorMessage = '';
  let httpStatus = null;

  if (isManualCheck) {
    log(`*** MANUAL CHECK REQUESTED: Checking monitor "${monitor.name}" (ID: ${monitor.id}) ***`);
  }

  log(`Checking monitor: ${monitor.name} (${monitor.type})`);

  // Get degraded thresholds
  const thresholds = await getDegradedThresholds();

  try {
    switch (monitor.type) {
      case 'http':
        // HTTP check
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000);

        try {
          const response = await axios.get(monitor.target, {
            signal: controller.signal,
            timeout: monitor.timeout * 1000,
            validateStatus: null, // Don't throw on non-2xx responses
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)'
            }
          });

          clearTimeout(timeoutId);
          httpStatus = response.status;
          responseTime = Date.now() - startTime;

          // Determine status based on HTTP status code and response time
          if (response.status >= 200 && response.status < 300) {
            // Success status codes
            const degradedThreshold = monitor.degraded_threshold || thresholds.response_time;

            if (responseTime > degradedThreshold) {
              status = 'degraded';
              errorMessage = `Slow response time: ${responseTime}ms (threshold: ${degradedThreshold}ms)`;
            } else {
              status = 'up';
            }
          } else if (thresholds.status_codes.includes(response.status)) {
            // Status codes that indicate degraded service
            status = 'degraded';
            errorMessage = `HTTP status indicates degraded service: ${response.status}`;
          } else {
            // All other status codes are considered down
            status = 'down';
            errorMessage = `HTTP status: ${response.status}`;
          }
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
        break;

      case 'ping':
        // Ping check (implemented as a simple HTTP HEAD request)
        try {
          log(`Sending ping request to ${monitor.target}...`);

          // Use a HEAD request for faster response
          const pingOptions = {
            method: 'HEAD',
            timeout: monitor.timeout * 1000,
            validateStatus: null, // Don't throw on non-2xx responses
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)',
              'Cache-Control': 'no-cache'
            }
          };

          try {
            const pingResponse = await axios(monitor.target, pingOptions);

            // For ping, we consider any response as successful
            status = 'up';
            responseTime = Date.now() - startTime;

            log(`Ping response received: Status ${pingResponse.status}, Time ${responseTime}ms`);
          } catch (error) {
            // For ping, we'll try a GET request as a fallback
            log(`HEAD request failed, trying GET request as fallback...`);

            const getOptions = {
              method: 'GET',
              timeout: monitor.timeout * 1000,
              validateStatus: null,
              headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)',
                'Cache-Control': 'no-cache'
              }
            };

            try {
              const getResponse = await axios(monitor.target, getOptions);

              // For ping, we consider any response as successful
              status = 'up';
              responseTime = Date.now() - startTime;

              log(`GET response received: Status ${getResponse.status}, Time ${responseTime}ms`);
            } catch (innerError) {
              // If both HEAD and GET fail, the host is down
              status = 'down';
              responseTime = Date.now() - startTime;
              throw new Error(`Ping failed: ${innerError.message}`);
            }
          }
        } catch (error) {
          log(`Ping request error: ${error.message}`, 'ERROR');
          status = 'down';
          throw error;
        }
        break;

      case 'port':
        // Port check
        try {
          log(`Checking port(s) for ${monitor.target}...`);

          // Import required modules
          const dns = require('dns');
          const net = require('net');
          const util = require('util');
          const lookupPromise = util.promisify(dns.lookup);

          // Parse target format: hostname|port1,port2,port3
          const [hostname, portsStr] = monitor.target.split('|');
          const ports = portsStr.split(',').map(p => parseInt(p.trim(), 10));

          log(`Host: ${hostname}, Ports: ${ports.join(', ')}`);

          // Resolve hostname to IP
          const hostIp = await lookupPromise(hostname);
          log(`Host ${hostname} resolved successfully to ${JSON.stringify(hostIp)}.`);

          // Check each port
          const portResults = [];
          let anyPortOpen = false;

          for (const port of ports) {
            try {
              const isOpen = await new Promise((resolve) => {
                const socket = new net.Socket();

                socket.setTimeout(monitor.timeout * 1000 || 30000);

                socket.on('connect', () => {
                  socket.destroy();
                  resolve(true);
                });

                socket.on('timeout', () => {
                  socket.destroy();
                  resolve(false);
                });

                socket.on('error', () => {
                  socket.destroy();
                  resolve(false);
                });

                socket.connect(port, hostIp.address);
              });

              if (isOpen) {
                anyPortOpen = true;
                log(`Port ${port} is open (TCP).`);
              } else {
                log(`Port ${port} is closed (TCP).`);
              }

              portResults.push({ port, status: isOpen });
            } catch (err) {
              log(`Error checking port ${port}: ${err.message}`, 'ERROR');
              portResults.push({ port, status: false, error: err.message });
            }
          }

          // If any port is open, consider the monitor up
          status = anyPortOpen ? 'up' : 'down';
          responseTime = Date.now() - startTime;

          if (!anyPortOpen) {
            errorMessage = `All ports are closed: ${ports.join(', ')}`;
          }
        } catch (error) {
          log(`Port check failed: ${error.message}`, 'ERROR');
          status = 'down';
          errorMessage = error.message;
        }
        break;

      default:
        // For any other monitor type, just mark it as up for now
        log(`Monitor type '${monitor.type}' is not fully implemented yet, marking as UP`, 'WARN');
        status = 'up';
        responseTime = 0;
    }
  } catch (error) {
    status = 'down';
    errorMessage = `Error: ${error.message}`;
    log(`Check failed for monitor ${monitor.name}: ${error.message}`, 'ERROR');
  }

  const checkResult = {
    monitor_id: monitor.id,
    status,
    response_time: responseTime,
    error_message: errorMessage,
    timestamp: new Date().toISOString()
  };

  // Save the check result to the database
  try {
    // Use RPC function to handle potential type conversion
    const { error } = await supabase
      .rpc('insert_monitor_history', {
        p_monitor_id: checkResult.monitor_id,
        p_status: checkResult.status === 'up', // Convert string status to boolean for monitor_history
        p_response_time: checkResult.response_time,
        p_error_message: checkResult.error_message,
        p_timestamp: checkResult.timestamp
      });

    if (error) {
      throw error;
    }

    // Update the monitor's status in the monitors table
    const { error: updateError } = await supabase
      .from('monitors')
      .update({
        status: status,
        last_check_time: checkResult.timestamp,
        last_response_time: responseTime
      })
      .eq('id', monitor.id);

    if (updateError) {
      log(`Error updating monitor status: ${updateError.message}`, 'ERROR');
    }

    log(`Saved check result for ${monitor.name}: ${status.toUpperCase()} (${responseTime}ms)`);

    // Check if status changed and send notification if needed
    await checkStatusChange(monitor, status);

  } catch (error) {
    log(`Failed to save check result: ${error.message}`, 'ERROR');
  }
}

// Check if the monitor status has changed and send a notification if needed
async function checkStatusChange(monitor, currentStatus) {
  try {
    // Get the previous check
    const { data, error } = await supabase
      .from('monitor_history')
      .select('status')
      .eq('monitor_id', monitor.id)
      .order('timestamp', { ascending: false })
      .range(1, 1); // Skip the most recent (current) check

    if (error) {
      throw error;
    }

    // If there's a previous check and the status has changed
    if (data && data.length > 0 && data[0].status !== currentStatus) {
      log(`Status change detected for ${monitor.name}: ${data[0].status} -> ${currentStatus}`);

      // Create a notification with appropriate message based on status
      let message = '';
      let type = 'other';

      if (currentStatus === 'up') {
        message = `Monitor ${monitor.name} is now operational`;
        type = 'up';
      } else if (currentStatus === 'degraded') {
        message = `Monitor ${monitor.name} is experiencing degraded performance`;
        type = 'other';
      } else if (currentStatus === 'down') {
        message = `Monitor ${monitor.name} is down`;
        type = 'down';
      }

      const notification = {
        monitor_id: monitor.id,
        message: message,
        type: type,
        timestamp: new Date().toISOString()
      };

      // Find all companies associated with this monitor
      const { data: monitorCompanies, error: companiesError } = await supabase
        .from('monitor_companies')
        .select('company_id')
        .eq('monitor_id', monitor.id);

      if (companiesError) {
        throw companiesError;
      }

      // Create notifications for each company
      if (monitorCompanies && monitorCompanies.length > 0) {
        for (const mc of monitorCompanies) {
          const companyNotification = {
            ...notification,
            company_id: mc.company_id
          };

          const { error: notificationError } = await supabase
            .from('notifications')
            .insert(companyNotification);

          if (notificationError) {
            log(`Error creating notification for company ${mc.company_id}: ${notificationError.message}`, 'ERROR');
          } else {
            // Send email notification to company admins
            try {
              await sendEmailNotification(monitor, currentStatus, mc.company_id);
            } catch (emailError) {
              log(`Error sending email notification for company ${mc.company_id}: ${emailError.message}`, 'ERROR');
            }
          }
        }

        log(`Created notifications for ${monitor.name} across ${monitorCompanies.length} companies`);
      } else {
        // If no companies are associated, create a general notification
        // Make sure we have a company_id for the notification
        if (!monitor.company_id) {
          log(`Monitor ${monitor.name} has no company_id and no associated companies. Cannot create notification.`, 'ERROR');
          return;
        }

        const generalNotification = {
          ...notification,
          company_id: monitor.company_id
        };

        const { error: notificationError } = await supabase
          .from('notifications')
          .insert(generalNotification);

        if (notificationError) {
          throw notificationError;
        }

        log(`Created notification for ${monitor.name}`);
      }
    }
  } catch (error) {
    log(`Failed to check status change: ${error.message}`, 'ERROR');
  }
}

// Import the direct email sender
const { sendEmailNotification: sendDirectEmail } = require('./direct-email-sender');

// This function has been removed as user_id is no longer used in notifications

// Send email notification to company admins
async function sendEmailNotification(monitor, status, companyId) {
  try {
    // Check if email alerts are enabled
    const enableEmailAlerts = process.env.ENABLE_EMAIL_ALERTS === 'true';

    if (!enableEmailAlerts) {
      log(`Email alerts are disabled. Skipping email notification for monitor ${monitor.name} (${status}) to company ${companyId}`);
      return true; // Return true to indicate "success" (as in, we did what was configured)
    }

    log(`Sending email notification for monitor ${monitor.name} (${status}) to company ${companyId}`);

    // Use the direct email sender instead of the Edge Function
    const success = await sendDirectEmail(monitor.id, status, companyId);

    if (success) {
      log(`Email notification sent successfully for monitor ${monitor.name} to company ${companyId}`);
      return true;
    } else {
      throw new Error('Failed to send email notification');
    }
  } catch (error) {
    log(`Error sending email notification: ${error.message}`, 'ERROR');
    return false;
  }
}

// Schedule a monitor for checking
function scheduleMonitor(monitor) {
  // Clear any existing schedule for this monitor
  if (monitorSchedules.has(monitor.id)) {
    clearTimeout(monitorSchedules.get(monitor.id));
  }

  // Function to check the monitor and reschedule
  const checkAndReschedule = async () => {
    try {
      // Get the last check time
      const { data: lastCheck, error: lastCheckError } = await supabase
        .from('monitor_history')
        .select('timestamp')
        .eq('monitor_id', monitor.id)
        .order('timestamp', { ascending: false })
        .limit(1);

      if (lastCheckError) {
        throw lastCheckError;
      }

      const now = Date.now();
      const lastCheckTime = lastCheck && lastCheck.length > 0
        ? new Date(lastCheck[0].timestamp).getTime()
        : 0;
      const intervalMs = (monitor.interval || 5) * 60 * 1000; // Default to 5 minutes

      // Check if it's time to check this monitor
      if (now - lastCheckTime >= intervalMs) {
        log(`Time to check monitor ${monitor.name} (interval: ${monitor.interval || 5} minutes)`);

        // Add the check to the worker pool
        await workerPool.addTask(() => performCheck(monitor));
      }

      // Schedule the next check
      const nextCheckTime = Math.max(1000, intervalMs - (now - lastCheckTime));
      monitorSchedules.set(monitor.id, setTimeout(checkAndReschedule, nextCheckTime));

    } catch (error) {
      log(`Error scheduling monitor ${monitor.name}: ${error.message}`, 'ERROR');

      // Retry after a delay
      monitorSchedules.set(monitor.id, setTimeout(checkAndReschedule, 60000)); // Retry after 1 minute
    }
  };

  // Start the schedule
  checkAndReschedule();
}

// Load all active monitors and schedule them
async function loadAndScheduleMonitors() {
  try {
    log('Loading monitors from database...');

    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (error) {
      throw error;
    }

    log(`Loaded ${monitors.length} active monitors`);

    // Schedule each monitor
    monitors.forEach(monitor => {
      scheduleMonitor(monitor);
    });
  } catch (error) {
    log(`Failed to load monitors: ${error.message}`, 'ERROR');

    // Retry after a delay
    setTimeout(loadAndScheduleMonitors, 60000); // Retry after 1 minute
  }
}

// Periodically check for new or updated monitors
async function checkForMonitorUpdates() {
  // Log that we're checking for updates (every 10 seconds)
  log(`Checking for monitor updates... (${new Date().toISOString()})`);

  try {
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (error) {
      throw error;
    }

    // Get current monitor IDs
    const currentMonitorIds = new Set(monitorSchedules.keys());
    const newMonitorIds = new Set(monitors.map(m => m.id));

    // Find monitors to add and remove
    const monitorsToAdd = monitors.filter(m => !currentMonitorIds.has(m.id));
    const monitorsToRemove = Array.from(currentMonitorIds).filter(id => !newMonitorIds.has(id));

    // Schedule new monitors
    monitorsToAdd.forEach(monitor => {
      log(`New monitor detected: ${monitor.name}`);
      scheduleMonitor(monitor);
    });

    // Remove old monitors
    monitorsToRemove.forEach(id => {
      log(`Monitor removed: ${id}`);
      if (monitorSchedules.has(id)) {
        clearTimeout(monitorSchedules.get(id));
        monitorSchedules.delete(id);
      }
    });

    // Check for updated monitors
    monitors.forEach(monitor => {
      if (currentMonitorIds.has(monitor.id)) {
        // Re-schedule to pick up any changes to interval, etc.
        scheduleMonitor(monitor);
      }
    });

    // Log the result of the check
    log(`Monitor update check: Found ${monitors.length} active monitors, ${monitorsToAdd.length} new, ${monitorsToRemove.length} to remove`);
    log(`Monitor service is running. Active monitors: ${monitors.length}`);

    if (monitors.length === 0) {
      log(`No active monitors found. The service is running but has nothing to check.`);
    }
  } catch (error) {
    log(`Failed to check for monitor updates: ${error.message}`, 'ERROR');
  }

  // Schedule next check
  setTimeout(checkForMonitorUpdates, CHECK_INTERVAL);
}

// Start the service
async function startService() {
  log('Starting VUM Monitor Service...');
  log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  log(`Supabase URL: ${SUPABASE_URL ? 'Configured' : 'Not configured'}`);
  log(`Log file: ${LOG_FILE}`);
  log(`Max concurrent checks: ${MAX_CONCURRENT_CHECKS}`);
  log(`Health check port: ${HEALTH_CHECK_PORT}`);

  try {
    // Start health check server
    const healthServer = await startHealthCheckServer();
    
    // Initial load of monitors
    await loadAndScheduleMonitors();

    // Start checking for monitor updates
    setInterval(checkForMonitorUpdates, 30000); // Check every 30 seconds for updates

    log('VUM Monitor Service is running. Press Ctrl+C to stop.');
    
    // Return the server for graceful shutdown
    return healthServer;
  } catch (error) {
    log(`Failed to start service: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Handle graceful shutdown
async function shutdown(server) {
  log('Shutting down Monitor Service...');

  try {
    // Close the health check server if it exists
    if (server) {
      await new Promise((resolve) => {
        server.close(() => {
          log('Health check server closed');
          resolve();
        });
      });
    }

    // Clear all intervals and timeouts
    const { interval } = require('timers');
    const { _timeouts } = process;

    // Clear timeouts
    if (_timeouts) {
      Object.keys(_timeouts).forEach(clearTimeout);
    }

    // Clear intervals
    if (interval) {
      Object.keys(interval).forEach(clearInterval);
    }

    log('Monitor Service has been stopped.');
    process.exit(0);
  } catch (error) {
    log(`Error during shutdown: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Handle shutdown signals
['SIGINT', 'SIGTERM'].forEach(signal => {
  process.on(signal, () => {
    log(`Received ${signal}, shutting down...`);
    shutdown();
  });
});

// Start the service with error handling
startService()
  .then((server) => {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      log(`Uncaught exception: ${error.message}`, 'ERROR');
      log(error.stack, 'ERROR');
      shutdown(server);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      log(`Unhandled Rejection at: ${promise}, reason: ${reason}`, 'ERROR');
    });
  })
  .catch((error) => {
    log(`Failed to start service: ${error.message}`, 'ERROR');
    process.exit(1);
  });
