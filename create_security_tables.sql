-- Enable pgcrypto for UUID generation
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Enable pg_cron for scheduled cleanup
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Create request_logs table
CREATE TABLE IF NOT EXISTS request_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    method TEXT NOT NULL,
    url TEXT NOT NULL,
    ip TEXT NOT NULL,
    status_code INTEGER NOT NULL,
    response_time INTEGER NOT NULL,
    user_agent TEXT,
    referrer TEXT,
    authenticated BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index on timestamp for faster cleanup
CREATE INDEX IF NOT EXISTS idx_request_logs_timestamp ON request_logs(timestamp);

-- Create index on IP for faster lookups
CREATE INDEX IF NOT EXISTS idx_request_logs_ip ON request_logs(ip);

-- <PERSON>reate function to get request stats
CREATE OR REPLACE FUNCTION get_request_stats(
    time_window INTERVAL DEFAULT INTERVAL '24 hours'
)
RETURNS TABLE (
    total_requests BIGINT,
    average_response_time NUMERIC,
    error_count BIGINT,
    unique_ips BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::BIGINT as total_requests,
        AVG(response_time)::NUMERIC as average_response_time,
        COUNT(*) FILTER (WHERE status_code >= 400)::BIGINT as error_count,
        COUNT(DISTINCT ip)::BIGINT as unique_ips
    FROM request_logs
    WHERE timestamp > NOW() - time_window;
END;
$$;

-- Create RLS policies
ALTER TABLE request_logs ENABLE ROW LEVEL SECURITY;

-- Only allow service role to insert
CREATE POLICY insert_request_logs ON request_logs
    FOR INSERT TO authenticated
    WITH CHECK (auth.role() = 'service_role');

-- Only allow service role to select
CREATE POLICY select_request_logs ON request_logs
    FOR SELECT TO authenticated
    USING (auth.role() = 'service_role');

-- Create view for monitoring dashboard
CREATE OR REPLACE VIEW request_stats_view AS
SELECT
    date_trunc('hour', timestamp) as time_bucket,
    COUNT(*) as request_count,
    AVG(response_time)::INTEGER as avg_response_time,
    COUNT(*) FILTER (WHERE status_code >= 400) as error_count,
    COUNT(DISTINCT ip) as unique_ips
FROM request_logs
WHERE timestamp > NOW() - INTERVAL '24 hours'
GROUP BY time_bucket
ORDER BY time_bucket DESC;
