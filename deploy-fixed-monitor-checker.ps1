# PowerShell script to deploy the fixed monitor-checker Edge Function

# Set the Supabase project reference
$projectRef = "axcfqilzeombkbzebeym"

# Create a zip file of the monitor-checker function
Write-Host "Creating zip file of the monitor-checker function..."
Compress-Archive -Path "supabase/functions/monitor-checker/*" -DestinationPath "monitor-checker-fixed.zip" -Force

# Deploy the function using the REST API
Write-Host "Deploying the function to Supabase..."
$token = $env:SUPABASE_ACCESS_TOKEN

if (-not $token) {
    Write-Host "SUPABASE_ACCESS_TOKEN environment variable not set. Please set it and try again."
    Write-Host "You can get your access token from https://supabase.com/dashboard/account/tokens"
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Get the function ID
$functionsResponse = Invoke-RestMethod -Uri "https://api.supabase.com/v1/projects/$projectRef/functions" -Headers $headers -Method Get
$monitorCheckerFunction = $functionsResponse | Where-Object { $_.name -eq "monitor-checker" }

if (-not $monitorCheckerFunction) {
    Write-Host "Function 'monitor-checker' not found. Please create it first."
    exit 1
}

$functionId = $monitorCheckerFunction.id

# Update the function
$updateResponse = Invoke-RestMethod -Uri "https://api.supabase.com/v1/projects/$projectRef/functions/$functionId" -Headers $headers -Method Patch -InFile "monitor-checker-fixed.zip"

Write-Host "Function deployed successfully!"
Write-Host "You can now use the 'Check Now' button to check monitors immediately."

# Clean up
Remove-Item "monitor-checker-fixed.zip" -Force
