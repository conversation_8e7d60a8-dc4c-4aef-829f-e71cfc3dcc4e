# PowerShell script to deploy SQL functions to Supabase
# This script reads the SQL file and executes it using the Supabase dashboard

# SQL file to execute
$sqlFile = "C:\VUM\fix_monitor_company_update.sql"

if (-not (Test-Path $sqlFile)) {
    Write-Host "Error: SQL file '$sqlFile' not found." -ForegroundColor Red
    exit 1
}

# Read the SQL file content
$sqlContent = Get-Content $sqlFile -Raw

# Display instructions for manual execution
Write-Host "To deploy the SQL functions, please follow these steps:" -ForegroundColor Cyan
Write-Host "1. Open the Supabase dashboard in your browser" -ForegroundColor Yellow
Write-Host "2. Navigate to the SQL Editor" -ForegroundColor Yellow
Write-Host "3. Create a new query" -ForegroundColor Yellow
Write-Host "4. Copy and paste the following SQL code:" -ForegroundColor Yellow
Write-Host "----------------------------------------------" -ForegroundColor Gray
Write-Host $sqlContent -ForegroundColor White
Write-Host "----------------------------------------------" -ForegroundColor Gray
Write-Host "5. Click 'Run' to execute the SQL" -ForegroundColor Yellow
Write-Host "6. Verify that the functions were created successfully" -ForegroundColor Yellow

# Open the Supabase dashboard
$openDashboard = Read-Host "Would you like to open the Supabase dashboard now? (y/n)"
if ($openDashboard -eq "y") {
    Start-Process "https://app.supabase.com/project/axcfqilzeombkbzebeym/sql"
}

Write-Host "SQL functions are ready to be deployed manually." -ForegroundColor Green
