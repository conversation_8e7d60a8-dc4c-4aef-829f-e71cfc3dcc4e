# Removing user_id from Notifications Table

This document describes the changes made to remove the `user_id` field from the notifications table in the Vurbis Uptime Monitor (VUM) application.

## Overview of Changes

The `user_id` field has been removed from the notifications table, and the application now uses `company_id` to determine which users should receive notifications.

### Key Changes

1. **Database Schema**:
   - Removed the `user_id` column from the `notifications` table
   - Updated RLS policies to use `company_id` instead of `user_id`
   - Updated database functions that create notifications

2. **Backend Code**:
   - Updated all monitor service implementations to no longer include `user_id` when creating notifications
   - Removed the `getDefaultUserId` function as it's no longer needed

3. **Frontend Code**:
   - Updated the notification interface to remove the `user_id` field
   - Modified queries to fetch notifications based on company membership instead of user ID
   - Updated real-time subscriptions to listen for notifications by company ID

## Technical Implementation

### Database Changes

The SQL script `remove_user_id_from_notifications.sql` makes the following changes:

1. Drops the foreign key constraint on `user_id`
2. Updates RLS policies to use company membership instead of user ID
3. Updates database functions to no longer use `user_id`
4. Removes the `user_id` column from the table

### Backend Code Changes

1. **database.js**:
   - Removed `user_id` from notification creation
   - Removed the `getDefaultUserId` function
   - Updated module exports

2. **concurrent-monitor-service.js**:
   - Removed `user_id` from notification creation
   - Removed the `getDefaultUserId` function

3. **monitor-service.js**:
   - Removed `user_id` from notification creation

### Frontend Code Changes

1. **use-notifications.ts**:
   - Updated the `Notification` interface to remove `user_id`
   - Modified `getNotifications` to fetch notifications based on company membership
   - Updated `markAllAsRead` to work with company IDs
   - Modified real-time subscriptions to listen for notifications by company ID

## Benefits of the Changes

1. **Simplified Data Model**: The notifications table now has a cleaner structure without redundant fields.
2. **Improved Security**: Access control is now based solely on company membership, which is more consistent with the rest of the application.
3. **Reduced Errors**: No more issues with null `user_id` values in notifications.
4. **Better Performance**: Queries are now more efficient as they don't need to join on user ID.

## Migration Notes

When deploying these changes:

1. Run the `remove_user_id_from_notifications.sql` script to update the database schema and functions
2. Deploy the updated backend code
3. Deploy the updated frontend code

No user action is required after deployment, as the system will automatically start using company IDs for notifications.
