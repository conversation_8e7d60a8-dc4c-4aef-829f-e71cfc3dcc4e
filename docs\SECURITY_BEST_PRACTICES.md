# Security Best Practices

This document outlines security best practices for the Vurbis Uptime Monitor (VUM) application, with a focus on handling sensitive information like API keys and credentials.

## Environment Variables

### Never Hardcode Secrets

**IMPORTANT**: Never hardcode sensitive information such as API keys, passwords, or other credentials directly in your code. Always use environment variables.

```javascript
// ❌ BAD - Hardcoded secrets
const SUPABASE_KEY = 'secret-value'; // Never do this!

// ✅ GOOD - Using environment variables
const SUPABASE_KEY = process.env.SUPABASE_KEY;
```

### Using .env Files

1. **Local Development**: Use `.env` files to store environment variables for local development.
2. **Template Files**: Use `.env.example` files as templates with placeholder values.
3. **Git Exclusion**: Ensure that `.env` files are listed in `.gitignore` to prevent them from being committed to the repository.

### Setting Up Environment Variables

1. Copy the example file to create your local environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file to add your actual values:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   ```

3. For the monitor service, also set up its environment file:
   ```bash
   cp monitor-service/.env.example monitor-service/.env
   ```

### Checking for Required Variables

Always check that required environment variables are set before using them:

```javascript
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_KEY) {
  console.error('ERROR: Required environment variables are not set.');
  process.exit(1);
}
```

## Supabase Keys

### Types of Keys

- **Anon Key**: Used for client-side code with Row Level Security (RLS) policies.
- **Service Key**: Has full access and bypasses RLS. Only use in trusted server environments.

### Key Rotation

If you suspect a key has been compromised:

1. Go to the Supabase dashboard > Project Settings > API
2. Click "Rotate" next to the compromised key
3. Update your `.env` files with the new key
4. Restart any services using the key

## Pre-commit Hooks

We use pre-commit hooks to prevent accidentally committing sensitive information:

1. The hook runs `scripts/check-hardcoded-secrets.js` before each commit
2. It scans for patterns that might indicate hardcoded secrets
3. If potential secrets are found in code files, the commit is blocked

## Secure Deployment

When deploying to production:

1. Use environment variables provided by your hosting platform
2. Never store production secrets in your codebase or repository
3. Limit access to production environment variables to authorized personnel only

## Database Credentials

For database connections:

1. Use the minimum required permissions for each service
2. Rotate database passwords regularly
3. Store database credentials securely in environment variables

## Email Service Credentials

For email services like Resend:

1. Use API keys with the minimum required permissions
2. Store API keys in environment variables
3. Rotate API keys regularly

## Regular Security Audits

Perform regular security audits:

1. Run `node scripts/check-hardcoded-secrets.js` to scan for hardcoded secrets
2. Review code for security vulnerabilities
3. Keep dependencies updated to patch security issues

## Reporting Security Issues

If you discover a security vulnerability, please report it responsibly by contacting the project maintainers directly rather than creating a public issue.
