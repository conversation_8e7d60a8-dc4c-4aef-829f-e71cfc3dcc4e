# User Preferences System

This document describes the user preferences system in the Vurbis Uptime Monitor (VUM) application.

## Overview

The user preferences system allows users to customize their experience in the application. Currently, it supports:

- Date format (ISO, US, or European)
- Time format (12-hour or 24-hour)

## Implementation

### Database

User preferences are stored in the `user_preferences` table in the database:

```sql
CREATE TABLE public.user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    date_format TEXT DEFAULT 'iso',
    time_format TEXT DEFAULT '24h',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

### Database Functions

The application uses two database functions to manage user preferences:

1. `get_or_create_user_preferences()`: Gets the current user's preferences or creates default preferences if none exist
2. `update_user_preferences(p_date_format TEXT, p_time_format TEXT)`: Updates the current user's preferences

### Client-Side Implementation

The client-side implementation is in `src/utils/dateFormat.ts`. It provides:

1. Functions to get user preferences from the database or localStorage
2. Functions to format dates and times according to user preferences
3. A cache system to avoid repeated database calls

### Fallback Mechanism

The system has a multi-level fallback mechanism:

1. First, it tries to get preferences from the database (if the user is authenticated)
2. If that fails, it falls back to localStorage
3. If localStorage doesn't have saved preferences, it detects preferences from the browser/OS settings
4. If detection fails, it uses default values (ISO format for dates, 24-hour format for times)

## Usage

### Getting User Preferences

```typescript
import { getUserDateDisplayFormat, getUserTimeFormat } from '@/utils/dateFormat';

// Get the user's preferred date format
const dateFormat = getUserDateDisplayFormat(); // 'iso', 'us', or 'eu'

// Get the user's preferred time format
const timeFormat = getUserTimeFormat(); // '12h' or '24h'
```

### Formatting Dates and Times

```typescript
import { formatDate, formatTime, formatDateTime } from '@/utils/dateFormat';

// Format a date according to user preferences
const formattedDate = formatDate(new Date()); // e.g., '2023-05-15'

// Format a time according to user preferences
const formattedTime = formatTime(new Date()); // e.g., '14:30'

// Format a date and time according to user preferences
const formattedDateTime = formatDateTime(new Date()); // e.g., '2023-05-15 14:30'
```

### Updating User Preferences

User preferences can be updated using the `DateTimeFormatSettings` component:

```typescript
import { DateTimeFormatSettings } from '@/components/DateTimeFormatSettings';

// In your component
return (
  <DateTimeFormatSettings />
);
```

## Handling Unauthenticated Users

For unauthenticated users, the system falls back to localStorage and browser/OS settings. This ensures that even users who are not logged in get a consistent experience based on their preferences.

## Troubleshooting

If you encounter issues with user preferences:

1. Check if the user is authenticated
2. Check if the `user_preferences` table exists and has the correct structure
3. Check if the `get_or_create_user_preferences` and `update_user_preferences` functions exist and are accessible
4. Check the browser console for errors
5. Try clearing localStorage and reloading the page
