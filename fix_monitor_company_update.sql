-- This script fixes the issue with updating monitor company associations
-- Run this in the Supabase SQL Editor

-- Update the existing update_monitor_with_companies function to be more robust
CREATE OR REPLACE FUNCTION update_monitor_with_companies(
  monitor_id UUID,
  monitor_name TEXT DEFAULT NULL,
  monitor_target TEXT DEFAULT NULL,
  monitor_type TEXT DEFAULT NULL,
  monitor_interval INTEGER DEFAULT NULL,
  monitor_timeout INTEGER DEFAULT NULL,
  monitor_active BOOLEAN DEFAULT NULL,
  company_ids UUID[] DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  monitor_exists BOOLEAN;
  company_id UUID;
BEGIN
  -- Check if the monitor exists
  SELECT EXISTS(SELECT 1 FROM monitors WHERE id = monitor_id) INTO monitor_exists;

  IF NOT monitor_exists THEN
    RAISE EXCEPTION 'Monitor with ID % does not exist', monitor_id;
  END IF;

  -- Update the monitor
  UPDATE monitors
  SET
    name = COALESCE(monitor_name, name),
    target = COALESCE(monitor_target, target),
    type = COALESCE(monitor_type, type),
    interval = COALESCE(monitor_interval, interval),
    timeout = COALESCE(monitor_timeout, timeout),
    active = COALESCE(monitor_active, active)
  WHERE
    id = monitor_id;

  -- If company_ids is provided, update the monitor-company relationships
  IF company_ids IS NOT NULL THEN
    -- Delete existing relationships
    DELETE FROM monitor_companies
    WHERE monitor_companies.monitor_id = update_monitor_with_companies.monitor_id;

    -- Create new relationships if company_ids is not empty
    IF array_length(company_ids, 1) > 0 THEN
      -- Use a more reliable bulk insert method
      INSERT INTO monitor_companies (monitor_id, company_id)
      SELECT update_monitor_with_companies.monitor_id, c.id
      FROM unnest(company_ids) AS c(id);
    END IF;
  END IF;

  -- Get the updated monitor with its companies
  SELECT
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'target', m.target,
      'type', m.type,
      'interval', m.interval,
      'timeout', m.timeout,
      'active', m.active,
      'user_id', m.user_id,
      'created_at', m.created_at,
      'companies', COALESCE(
        (
          SELECT jsonb_agg(
            jsonb_build_object(
              'id', mc.id,
              'monitor_id', mc.monitor_id,
              'company_id', mc.company_id,
              'created_at', mc.created_at
            )
          )
          FROM monitor_companies mc
          WHERE mc.monitor_id = m.id
        ),
        '[]'::jsonb
      )
    ) INTO result
  FROM
    monitors m
  WHERE
    m.id = monitor_id;

  -- If no result was found, raise an exception
  IF result IS NULL THEN
    RAISE EXCEPTION 'Failed to retrieve updated monitor data';
  END IF;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
