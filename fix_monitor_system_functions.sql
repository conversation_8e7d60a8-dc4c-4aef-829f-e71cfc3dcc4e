-- Create a function to execute dynamic SQL (with proper security checks)
CREATE OR REP<PERSON>CE FUNCTION public.execute_sql(query_text TEXT)
RETURNS SETOF json AS $$
BEGIN
  -- Only allow SELECT queries for security
  IF position('SELECT' in upper(query_text)) != 1 THEN
    RAISE EXCEPTION 'Only SELECT queries are allowed';
  END IF;
  
  RETURN QUERY EXECUTE query_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get monitor history column information
CREATE OR REPLACE FUNCTION get_monitor_history_columns()
RETURNS TABLE (
  column_name text,
  data_type text,
  is_nullable text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.column_name::text,
    c.data_type::text,
    c.is_nullable::text
  FROM 
    information_schema.columns c
  WHERE 
    c.table_schema = 'public' 
    AND c.table_name = 'monitor_history'
  ORDER BY 
    c.ordinal_position;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get monitor status counts
CREATE OR REPLACE FUNCTION get_monitor_status_counts()
<PERSON><PERSON>URNS json AS $$
DECLARE
  result json;
BEGIN
  WITH latest_history AS (
    SELECT DISTINCT ON (monitor_id)
      monitor_id,
      status,
      error_message
    FROM
      monitor_history
    ORDER BY
      monitor_id,
      timestamp DESC
  )
  SELECT json_build_object(
    'total', COUNT(*),
    'up', SUM(CASE WHEN status = true THEN 1 ELSE 0 END),
    'down', SUM(CASE WHEN status = false THEN 1 ELSE 0 END)
  ) INTO result
  FROM latest_history;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
