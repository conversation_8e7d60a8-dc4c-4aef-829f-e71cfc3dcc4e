-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS public.execute_sql(text);
DROP FUNCTION IF EXISTS public.get_monitor_history_columns();
DROP FUNCTION IF EXISTS public.get_monitor_status_counts();

-- Create a function to execute dynamic SQL (with proper security checks)
CREATE OR REPLACE FUNCTION public.execute_sql(query_text TEXT)
RETURNS SETOF jsonb AS $$
BEGIN
  -- Only allow SELECT queries for security
  IF position('SELECT' in upper(query_text)) != 1 THEN
    RAISE EXCEPTION 'Only SELECT queries are allowed';
  END IF;
  
  RETURN QUERY EXECUTE query_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get monitor history column information
CREATE OR REPLACE FUNCTION public.get_monitor_history_columns()
RETURNS TABLE (
  column_name text,
  data_type text,
  is_nullable text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.column_name::text,
    c.data_type::text,
    c.is_nullable::text
  FROM 
    information_schema.columns c
  WHERE 
    c.table_schema = 'public' 
    AND c.table_name = 'monitor_history'
  ORDER BY 
    c.ordinal_position;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get monitor status counts
CREATE OR REPLACE FUNCTION public.get_monitor_status_counts()
RETURNS jsonb AS $$
DECLARE
  total_count integer;
  up_count integer;
  down_count integer;
BEGIN
  -- Get counts from latest monitor history
  WITH latest_history AS (
    SELECT DISTINCT ON (monitor_id)
      monitor_id,
      status
    FROM
      monitor_history
    ORDER BY
      monitor_id,
      timestamp DESC
  )
  SELECT
    COUNT(*)::integer,
    COUNT(CASE WHEN status = true THEN 1 END)::integer,
    COUNT(CASE WHEN status = false THEN 1 END)::integer
  INTO
    total_count,
    up_count,
    down_count
  FROM latest_history;
  
  -- Return JSON object with counts
  RETURN jsonb_build_object(
    'total', total_count,
    'up', up_count,
    'down', down_count
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.execute_sql(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_monitor_history_columns() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_monitor_status_counts() TO authenticated;
