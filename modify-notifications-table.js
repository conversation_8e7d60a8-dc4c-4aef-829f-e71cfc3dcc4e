// <PERSON>ript to modify the notifications table to make user_id nullable
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Function to modify the notifications table
async function modifyNotificationsTable() {
  try {
    console.log('Modifying notifications table to make user_id nullable...');
    
    // Execute a raw SQL query to modify the table
    const { data, error } = await supabase.rpc('execute_sql', {
      query: `
        ALTER TABLE notifications
        ALTER COLUMN user_id DROP NOT NULL;
      `
    });
    
    if (error) {
      throw error;
    }
    
    console.log('Successfully modified notifications table!');
    console.log('The user_id column is now nullable.');
    
    return { success: true };
  } catch (error) {
    console.error(`Error modifying notifications table: ${error.message}`);
    
    // Try an alternative approach if the first one fails
    try {
      console.log('Trying alternative approach...');
      
      // Use the REST API to execute SQL
      const { data, error } = await supabase.from('_rpc').select('*').execute('execute_sql', {
        query: `
          ALTER TABLE notifications
          ALTER COLUMN user_id DROP NOT NULL;
        `
      });
      
      if (error) {
        throw error;
      }
      
      console.log('Successfully modified notifications table using alternative approach!');
      return { success: true };
    } catch (altError) {
      console.error(`Alternative approach failed: ${altError.message}`);
      
      console.log('\nManual SQL Instructions:');
      console.log('------------------------');
      console.log('Please execute the following SQL in the Supabase SQL Editor:');
      console.log('\nALTER TABLE notifications ALTER COLUMN user_id DROP NOT NULL;');
      console.log('\nThis will make the user_id column nullable and fix the notification errors.');
      
      return { success: false, error: error.message };
    }
  }
}

// Run the function
modifyNotificationsTable()
  .then(result => {
    if (result.success) {
      console.log('Operation completed successfully.');
    } else {
      console.error('Operation failed. Please follow the manual instructions above.');
    }
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error(`Unexpected error: ${error.message}`);
    process.exit(1);
  });
