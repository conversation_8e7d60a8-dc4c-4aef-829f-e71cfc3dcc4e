# Logs
logs
*.log
npm-debug.log*

# Dependency directories
node_modules/

# Environment variables
.env
.env.example

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# dotenv environment variable files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Windows service files
daemon/
