# VUM Monitor Service

A Node.js background service that continuously checks monitors at their configured intervals.

## Features

- Platform-independent Node.js service (works on Windows, macOS, Linux)
- **NEW: Concurrent monitoring** - Checks multiple monitors simultaneously
- **NEW: Configurable concurrency** - Set the maximum number of concurrent checks
- Automatically checks monitors based on their configured intervals
- Creates notifications when monitor status changes
- Handles errors gracefully with automatic retries
- Provides detailed logging
- Configurable via environment variables

## Installation

### Prerequisites

- Node.js 14.x or higher
- npm (comes with Node.js)

### Steps

1. **Install dependencies**

   ```bash
   cd monitor-service
   npm install
   ```

2. **Configure the service**

   Edit the `.env` file to set your configuration:

   ```
   # Supabase Configuration
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key

   # Monitor Service Configuration
   CHECK_INTERVAL=60000  # Check every 60 seconds (60000 ms)
   LOG_LEVEL=info        # Log level (debug, info, warn, error)
   MONITOR_TABLE=monitors
   HISTORY_TABLE=monitor_history
   NOTIFICATION_TABLE=notifications
   MAX_CONCURRENT_CHECKS=10  # Maximum number of concurrent checks
   ```

3. **Start the service**

   ```bash
   npm run start-concurrent
   ```

## Logs

Logs are stored in the `logs` directory:

- `monitor-service.log`: Contains all log messages
- `error.log`: Contains only error messages

## Running as a Background Service

### Using PM2 (Recommended)

[PM2](https://pm2.keymetrics.io/) is a process manager for Node.js applications that keeps applications alive forever and reloads them without downtime.

1. **Install PM2 globally**

   ```bash
   npm install -g pm2
   ```

2. **Start the service with PM2**

   ```bash
   pm2 start concurrent-monitor-service.js --name "vum-monitor-service"
   ```

3. **Set up PM2 to start on system boot**

   ```bash
   pm2 startup
   pm2 save
   ```

### Using systemd (Linux)

1. **Create a systemd service file**

   ```bash
   sudo nano /etc/systemd/system/vum-monitor-service.service
   ```

2. **Add the following content**

   ```
   [Unit]
   Description=VUM Monitor Service
   After=network.target

   [Service]
   Type=simple
   User=your_username
   WorkingDirectory=/path/to/monitor-service
   ExecStart=/usr/bin/node /path/to/monitor-service/concurrent-monitor-service.js
   Restart=on-failure
   RestartSec=10
   StandardOutput=syslog
   StandardError=syslog
   SyslogIdentifier=vum-monitor-service

   [Install]
   WantedBy=multi-user.target
   ```

3. **Enable and start the service**

   ```bash
   sudo systemctl enable vum-monitor-service
   sudo systemctl start vum-monitor-service
   ```

## How It Works

1. The service connects to your Supabase database and loads all active monitors
2. It creates a worker pool that can run multiple checks simultaneously
3. For each monitor, it schedules when it should next be checked based on:
   - The monitor's configured interval
   - The timestamp of the most recent check
4. When a monitor is due for a check, it's added to the worker pool
5. The worker pool processes up to `MAX_CONCURRENT_CHECKS` monitors simultaneously
6. Results are saved to the database and notifications are sent if needed
7. The service periodically checks for new or updated monitors

## Troubleshooting

If you encounter issues:

1. **Check the logs**
   - Look in the `logs/monitor-service.log` and `logs/error.log` files
   - The logs contain detailed information about service operation and any errors

2. **Verify database connection**
   - Make sure your Supabase URL and API key are correct in the `.env` file
   - Check that the tables exist and have the expected structure

3. **Check monitor configuration**
   - Verify that monitors have valid values for `interval` and `timeout`
   - Make sure the `target` URLs are valid

4. **Try running manually first**
   - Run the service directly to see any startup errors:
     ```bash
     node concurrent-monitor-service.js
     ```

5. **Check Node.js installation**
   - Verify that Node.js is installed correctly:
     ```bash
     node --version
     ```
   - Make sure it's version 14.x or higher

6. **Reinstall dependencies**
   - Try reinstalling the Node.js dependencies:
     ```bash
     npm ci
     ```
   - This will do a clean install of all dependencies

7. **Restart the service**
   - If using PM2:
     ```bash
     pm2 restart vum-monitor-service
     ```
   - If using systemd (Linux):
     ```bash
     sudo systemctl restart vum-monitor-service
     ```
