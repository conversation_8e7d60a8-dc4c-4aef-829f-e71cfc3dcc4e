// <PERSON>ript to activate all monitors in the database
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function activateMonitors() {
  console.log('Checking monitor status...');

  try {
    // Get all monitors
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*');

    if (error) {
      throw error;
    }

    console.log(`Found ${monitors.length} total monitors in the database`);

    // Display current status
    monitors.forEach(monitor => {
      console.log(`- ${monitor.name}: active = ${monitor.active}`);
    });

    // Count inactive monitors
    const inactiveMonitors = monitors.filter(m => m.active !== true);

    if (inactiveMonitors.length === 0) {
      console.log('\nAll monitors are already active. No changes needed.');
      return;
    }

    console.log(`\nFound ${inactiveMonitors.length} inactive monitors. Activating...`);

    // Update all monitors to active=true
    const { error: updateError } = await supabase
      .from('monitors')
      .update({ active: true })
      .in('id', inactiveMonitors.map(m => m.id));

    if (updateError) {
      throw updateError;
    }

    console.log('Successfully activated all monitors!');

    // Verify the update
    const { data: updatedMonitors, error: verifyError } = await supabase
      .from('monitors')
      .select('*');

    if (verifyError) {
      throw verifyError;
    }

    console.log('\nUpdated monitor status:');
    updatedMonitors.forEach(monitor => {
      console.log(`- ${monitor.name}: active = ${monitor.active}`);
    });

  } catch (error) {
    console.error('Error:', error.message);
  }
}

activateMonitors();
