// <PERSON>ript to check the email alert setting
require('dotenv').config();

console.log('=== EMAIL ALERT SETTING CHECK ===');
console.log(`ENABLE_EMAIL_ALERTS: ${process.env.ENABLE_EMAIL_ALERTS}`);
console.log(`Type of ENABLE_EMAIL_ALERTS: ${typeof process.env.ENABLE_EMAIL_ALERTS}`);
console.log(`Is ENABLE_EMAIL_ALERTS === 'true'? ${process.env.ENABLE_EMAIL_ALERTS === 'true'}`);
console.log(`Is ENABLE_EMAIL_ALERTS == true? ${process.env.ENABLE_EMAIL_ALERTS == true}`);
