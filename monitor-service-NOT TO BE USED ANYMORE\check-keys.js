// Script to check the actual key values
require('dotenv').config();

console.log('=== KEY CHECK ===');

// Check if SUPABASE_KEY is actually the service key
const supabaseKey = process.env.SUPABASE_KEY;
const serviceKey = process.env.SUPABASE_SERVICE_KEY;

console.log(`SUPABASE_KEY: ${supabaseKey}`);
console.log(`SUPABASE_SERVICE_KEY: ${serviceKey}`);

// Check if variable substitution worked
if (supabaseKey === '${SUPABASE_SERVICE_KEY}') {
  console.log('WARNING: Variable substitution failed. SUPABASE_KEY contains the literal string "${SUPABASE_SERVICE_KEY}"');
}

// Check if the key is a service role key
if (supabaseKey && supabaseKey.includes('role=service_role')) {
  console.log('SUPABASE_KEY is a service role key');
} else if (supabase<PERSON>ey && supabaseKey.includes('role=anon')) {
  console.log('SUPABASE_KEY is an anon key');
} else {
  console.log('SUPABASE_KEY is not recognized as a service role or anon key');
}
