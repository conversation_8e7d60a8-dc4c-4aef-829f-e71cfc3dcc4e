// Script to check a specific monitor by ID
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const MONITOR_TABLE = process.env.MONITOR_TABLE || 'monitors';
const HISTORY_TABLE = process.env.HISTORY_TABLE || 'monitor_history';

// Get monitor ID from command line
const monitorId = process.argv[2];

if (!monitorId) {
  console.log('Please provide a monitor ID as a command line argument:');
  console.log('npm run check-monitor-by-id -- your-monitor-id');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkMonitorById(id) {
  console.log('=== CHECKING MONITOR BY ID ===');
  console.log(`Monitor ID: ${id}`);
  
  // Get the monitor from the database
  try {
    console.log('\nFetching monitor from database...');
    
    const { data: monitor, error } = await supabase
      .from(MONITOR_TABLE)
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) {
      console.log(`Error fetching monitor: ${error.message}`);
      return;
    }
    
    if (!monitor) {
      console.log(`No monitor found with ID: ${id}`);
      return;
    }
    
    console.log('Monitor found:');
    console.log(`Name: ${monitor.name}`);
    console.log(`Type: ${monitor.type}`);
    console.log(`Target: ${monitor.target}`);
    console.log(`Interval: ${monitor.interval} minutes`);
    console.log(`Timeout: ${monitor.timeout} seconds`);
    console.log(`Active: ${monitor.active}`);
    
    // Get the last check for this monitor
    const { data: lastCheck, error: lastCheckError } = await supabase
      .from(HISTORY_TABLE)
      .select('*')
      .eq('monitor_id', id)
      .order('timestamp', { ascending: false })
      .limit(1)
      .single();
      
    if (lastCheckError && lastCheckError.code !== 'PGRST116') {
      console.log(`Error fetching last check: ${lastCheckError.message}`);
    } else if (lastCheck) {
      console.log('\nLast check:');
      console.log(`Timestamp: ${lastCheck.timestamp}`);
      console.log(`Status: ${lastCheck.status ? 'UP' : 'DOWN'}`);
      console.log(`Response time: ${lastCheck.response_time}ms`);
      
      if (lastCheck.error_message) {
        console.log(`Error message: ${lastCheck.error_message}`);
      }
    } else {
      console.log('\nNo previous checks found for this monitor.');
    }
    
    // Perform a check
    console.log('\nPerforming a check now...');
    
    const startTime = Date.now();
    let status = false;
    let responseTime = null;
    let errorMessage = null;
    
    try {
      if (monitor.type === 'http') {
        console.log(`Sending HTTP request to ${monitor.target}...`);
        
        const response = await axios.get(monitor.target, {
          timeout: (monitor.timeout || 30) * 1000,
          validateStatus: null, // Don't throw on non-2xx responses
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)'
          }
        });
        
        responseTime = Date.now() - startTime;
        status = response.status >= 200 && response.status < 300;
        
        console.log(`Response received in ${responseTime}ms`);
        console.log(`Status code: ${response.status}`);
        console.log(`Status: ${status ? 'UP' : 'DOWN'}`);
        
        if (!status) {
          errorMessage = `HTTP status: ${response.status}`;
          console.log(`Error message: ${errorMessage}`);
        }
      } else {
        console.log(`Unsupported monitor type: ${monitor.type}`);
        errorMessage = `Unsupported monitor type: ${monitor.type}`;
      }
    } catch (error) {
      responseTime = Date.now() - startTime;
      status = false;
      errorMessage = `Error: ${error.message}`;
      
      console.log(`Check failed: ${error.message}`);
      console.log(`Response time: ${responseTime}ms`);
      console.log(`Status: DOWN`);
    }
    
    // Save the check result
    console.log('\nSaving check result to database...');
    
    const checkResult = {
      monitor_id: id,
      status,
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };
    
    const { error: saveError } = await supabase
      .from(HISTORY_TABLE)
      .insert(checkResult);
      
    if (saveError) {
      console.log(`Error saving check result: ${saveError.message}`);
    } else {
      console.log('Check result saved successfully!');
    }
    
    console.log('\n=== CHECK COMPLETE ===');
  } catch (error) {
    console.log(`Error: ${error.message}`);
  }
}

checkMonitorById(monitorId).catch(error => {
  console.error('Unhandled error:', error);
});
