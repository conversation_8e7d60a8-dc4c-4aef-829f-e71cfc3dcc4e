// Direct script to check the monitors table
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkMonitorsTable() {
  console.log('=== DIRECT MONITORS TABLE CHECK ===');
  console.log(`Supabase URL: ${SUPABASE_URL}`);
  console.log(`Supabase Key: ${SUPABASE_KEY.substring(0, 10)}...${SUPABASE_KEY.substring(SUPABASE_KEY.length - 5)}`);

  // Try different approaches to query the monitors table
  console.log('\n=== APPROACH 1: STANDARD SELECT ===');
  try {
    const { data, error } = await supabase
      .from('monitors')
      .select('*');

    if (error) {
      console.log(`Error: ${error.message}`);
    } else {
      console.log(`Success! Found ${data ? data.length : 0} records.`);
      if (data && data.length > 0) {
        console.log('\nFirst record:');
        console.log(data[0]);
      }
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  console.log('\n=== APPROACH 2: SELECT WITH COUNT ===');
  try {
    const { count, error } = await supabase
      .from('monitors')
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.log(`Error: ${error.message}`);
    } else {
      console.log(`Success! Count: ${count}`);
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  console.log('\n=== APPROACH 3: RAW SQL QUERY ===');
  try {
    const { data, error } = await supabase.rpc('execute_sql', {
      query_text: 'SELECT COUNT(*) FROM monitors'
    });

    if (error) {
      console.log(`Error: ${error.message}`);
      console.log('RPC function might not be available. Trying another approach...');
    } else {
      console.log(`Success! Result: ${JSON.stringify(data)}`);
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  console.log('\n=== APPROACH 4: SELECT WITH SPECIFIC COLUMNS ===');
  try {
    const { data, error } = await supabase
      .from('monitors')
      .select('id, name, type, target, interval, active');

    if (error) {
      console.log(`Error: ${error.message}`);
    } else {
      console.log(`Success! Found ${data ? data.length : 0} records.`);
      if (data && data.length > 0) {
        console.log('\nRecords:');
        data.forEach((record, index) => {
          console.log(`\nRecord #${index + 1}:`);
          console.log(`ID: ${record.id}`);
          console.log(`Name: ${record.name}`);
          console.log(`Type: ${record.type}`);
          console.log(`Target: ${record.target}`);
          console.log(`Interval: ${record.interval}`);
          console.log(`Active: ${record.active} (${typeof record.active})`);
        });
      }
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  console.log('\n=== APPROACH 5: CHECK TABLE STRUCTURE ===');
  try {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 'monitors');

    if (error) {
      console.log(`Error: ${error.message}`);
    } else {
      console.log(`Success! Found ${data ? data.length : 0} columns.`);
      if (data && data.length > 0) {
        console.log('\nColumns:');
        data.forEach(column => {
          console.log(`${column.column_name}: ${column.data_type}`);
        });
      }
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  console.log('\n=== APPROACH 6: CHECK PERMISSIONS ===');
  try {
    // Try to insert a dummy record and then delete it
    const dummyMonitor = {
      name: 'Test Monitor (Temporary)',
      type: 'http',
      target: 'https://www.example.com',
      interval: 5,
      timeout: 30,
      active: true
    };

    console.log('Attempting to insert a temporary record...');
    const { data: insertData, error: insertError } = await supabase
      .from('monitors')
      .insert(dummyMonitor)
      .select();

    if (insertError) {
      console.log(`Insert Error: ${insertError.message}`);
    } else {
      console.log('Insert successful!');

      // Now delete the record
      if (insertData && insertData.length > 0) {
        const { error: deleteError } = await supabase
          .from('monitors')
          .delete()
          .eq('id', insertData[0].id);

        if (deleteError) {
          console.log(`Delete Error: ${deleteError.message}`);
        } else {
          console.log('Delete successful!');
        }
      }
    }
  } catch (error) {
    console.log(`Exception: ${error.message}`);
  }

  console.log('\n=== CHECK COMPLETE ===');
}

checkMonitorsTable().catch(error => {
  console.error('Unhandled error:', error);
});
