/**
 * <PERSON><PERSON><PERSON> to check if the monitor service is running
 *
 * This script checks if the monitor service is running by:
 * 1. Checking if the process is running
 * 2. Checking if the service is responding to requests
 * 3. Checking the last log entry timestamp
 *
 * Usage: node check-service-status.js
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const LOG_FILE = path.join(__dirname, 'logs', 'monitor-service.log');

// Check if required environment variables are set
if (!SUPABASE_URL) {
  console.error('ERROR: Required environment variable SUPABASE_URL is not set.');
  console.error('Please set this variable in your .env file.');
  process.exit(1);
}

if (!SUPABASE_KEY) {
  console.error('ERROR: Required environment variable SUPABASE_KEY is not set.');
  console.error('Please set this variable in your .env file.');
  console.error('IMPORTANT: SUPABASE_KEY must be the service_role key from Supabase.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Check if the monitor service process is running
 * @returns {Promise<boolean>} True if the process is running, false otherwise
 */
async function isProcessRunning() {
  return new Promise((resolve) => {
    const command = process.platform === 'win32'
      ? 'tasklist /FI "IMAGENAME eq node.exe" /FO CSV /NH'
      : 'ps aux | grep "[n]ode.*concurrent-monitor-service"';

    exec(command, (error, stdout) => {
      if (error) {
        console.error(`Error checking process: ${error.message}`);
        resolve(false);
        return;
      }

      if (process.platform === 'win32') {
        // On Windows, we need to check if any of the node processes are running the monitor service
        // This is a simple check and might have false positives
        resolve(stdout.includes('node.exe'));
      } else {
        // On Unix-like systems, the grep command already filters for the monitor service
        resolve(stdout.trim() !== '');
      }
    });
  });
}

/**
 * Check if the service is responding to database queries
 * @returns {Promise<boolean>} True if the service is responding, false otherwise
 */
async function isServiceResponding() {
  try {
    // Try to query the monitors table
    const { data, error } = await supabase
      .from('monitors')
      .select('id')
      .limit(1);

    if (error) {
      console.error(`Error querying database: ${error.message}`);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error checking service: ${error.message}`);
    return false;
  }
}

/**
 * Check the last log entry timestamp
 * @returns {Promise<{isRecent: boolean, lastTimestamp: string}>} Object with isRecent flag and lastTimestamp
 */
async function checkLastLogTimestamp() {
  return new Promise((resolve) => {
    fs.access(LOG_FILE, fs.constants.F_OK, (err) => {
      if (err) {
        console.error(`Log file not found: ${LOG_FILE}`);
        resolve({ isRecent: false, lastTimestamp: 'N/A' });
        return;
      }

      // Read the last few lines of the log file
      // Use different commands for Windows and Unix-like systems
      const command = process.platform === 'win32'
        ? `powershell -Command "Get-Content -Tail 100 '${LOG_FILE}'"`
        : `tail -n 100 "${LOG_FILE}"`;

      exec(command, (error, stdout) => {
        if (error) {
          console.error(`Error reading log file: ${error.message}`);
          resolve({ isRecent: false, lastTimestamp: 'N/A' });
          return;
        }

        // Extract timestamps from log entries
        const timestampRegex = /\[(.*?)\]/;
        const lines = stdout.split('\n').filter(line => line.trim() !== '');

        if (lines.length === 0) {
          resolve({ isRecent: false, lastTimestamp: 'N/A' });
          return;
        }

        // Get the last line with a timestamp
        let lastTimestamp = 'N/A';
        for (let i = lines.length - 1; i >= 0; i--) {
          const match = lines[i].match(timestampRegex);
          if (match && match[1]) {
            lastTimestamp = match[1];
            break;
          }
        }

        if (lastTimestamp === 'N/A') {
          resolve({ isRecent: false, lastTimestamp });
          return;
        }

        // Check if the timestamp is recent (within the last 5 minutes)
        try {
          const lastLogTime = new Date(lastTimestamp);
          const now = new Date();
          const diffMinutes = (now - lastLogTime) / (1000 * 60);

          resolve({
            isRecent: diffMinutes < 5,
            lastTimestamp
          });
        } catch (e) {
          console.error(`Error parsing timestamp: ${e.message}`);
          resolve({ isRecent: false, lastTimestamp });
        }
      });
    });
  });
}

/**
 * Main function
 */
async function main() {
  console.log('Checking monitor service status...');

  // Check if the process is running
  const processRunning = await isProcessRunning();
  console.log(`Process running: ${processRunning ? 'YES' : 'NO'}`);

  // Check if the service is responding
  const serviceResponding = await isServiceResponding();
  console.log(`Service responding: ${serviceResponding ? 'YES' : 'NO'}`);

  // Check the last log entry timestamp
  const { isRecent, lastTimestamp } = await checkLastLogTimestamp();
  console.log(`Last log entry: ${lastTimestamp}`);
  console.log(`Log entries are recent: ${isRecent ? 'YES' : 'NO'}`);

  // Determine overall status
  const isRunning = processRunning && serviceResponding && isRecent;

  console.log('\nOverall status:');
  if (isRunning) {
    console.log('✅ Monitor service is RUNNING');
  } else {
    console.log('❌ Monitor service is NOT RUNNING');

    // Provide troubleshooting steps
    console.log('\nTroubleshooting steps:');
    if (!processRunning) {
      console.log('1. Start the monitor service with: npm run start');
    }
    if (!serviceResponding) {
      console.log('2. Check if the database connection is working');
      console.log('   - Verify SUPABASE_URL and SUPABASE_KEY in .env file');
      console.log('   - Make sure SUPABASE_KEY is the service_role key from Supabase');
      console.log('   - Make sure the service_role key is valid and not expired');
    }
    if (!isRecent) {
      console.log('3. Check the log file for errors:');
      console.log(`   - ${LOG_FILE}`);
    }
  }
}

// Run the script
main().catch(error => {
  console.error(`Error: ${error.message}`);
  process.exit(1);
});
