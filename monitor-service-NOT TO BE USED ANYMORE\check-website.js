// Script to diagnose website checking issues
require('dotenv').config();
const axios = require('axios');
const https = require('https');
const dns = require('dns');
const { promisify } = require('util');

// URL to check
const urlToCheck = process.argv[2] || 'https://www.zwankhuizen.com/';

// DNS lookup with promise
const dnsLookup = promisify(dns.lookup);
const dnsResolve = promisify(dns.resolve);

async function checkWebsite() {
  console.log('=== WEBSITE CHECK DIAGNOSTIC ===');
  console.log(`URL to check: ${urlToCheck}`);
  
  // Step 1: DNS resolution
  console.log('\n=== STEP 1: DNS RESOLUTION ===');
  try {
    console.log('Performing DNS lookup...');
    const { address, family } = await dnsLookup(new URL(urlToCheck).hostname);
    console.log(`Success! Resolved to IP: ${address} (IPv${family})`);
    
    // Try to get all DNS records
    console.log('\nChecking all DNS records...');
    const hostname = new URL(urlToCheck).hostname;
    
    try {
      const aRecords = await dnsResolve(hostname, 'A');
      console.log(`A records: ${aRecords.join(', ')}`);
    } catch (error) {
      console.log(`Could not get A records: ${error.message}`);
    }
    
    try {
      const cnameRecords = await dnsResolve(hostname, 'CNAME');
      console.log(`CNAME records: ${cnameRecords.join(', ')}`);
    } catch (error) {
      console.log(`Could not get CNAME records: ${error.message}`);
    }
  } catch (error) {
    console.log(`DNS resolution failed: ${error.message}`);
    console.log('This suggests the domain might not exist or DNS is not working properly.');
  }
  
  // Step 2: Basic HTTP request
  console.log('\n=== STEP 2: BASIC HTTP REQUEST ===');
  try {
    console.log('Sending basic HTTP request...');
    const startTime = Date.now();
    
    const response = await axios.get(urlToCheck, {
      timeout: 30000, // 30 seconds
      validateStatus: null, // Don't throw on non-2xx responses
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    console.log(`Response received in ${responseTime}ms`);
    console.log(`Status code: ${response.status}`);
    console.log(`Status text: ${response.statusText}`);
    console.log(`Content type: ${response.headers['content-type']}`);
    console.log(`Content length: ${response.headers['content-length'] || 'unknown'}`);
    
    if (response.status >= 200 && response.status < 300) {
      console.log('Success! The website is responding with a 2xx status code.');
    } else {
      console.log(`Warning: The website responded with a non-2xx status code: ${response.status}`);
      if (response.status >= 300 && response.status < 400) {
        console.log('This is a redirect. The monitor might need to be configured to follow redirects.');
      } else if (response.status >= 400 && response.status < 500) {
        console.log('This is a client error. The request might be malformed or unauthorized.');
      } else if (response.status >= 500) {
        console.log('This is a server error. The website might be experiencing issues.');
      }
    }
  } catch (error) {
    console.log(`HTTP request failed: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('Connection refused. The server might be down or blocking connections.');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('Connection timed out. The server might be slow or unreachable.');
    } else if (error.code === 'ENOTFOUND') {
      console.log('Domain not found. The domain might not exist or DNS is not working properly.');
    } else if (error.response) {
      console.log(`Received response with status: ${error.response.status}`);
    }
  }
  
  // Step 3: HTTPS certificate check
  if (urlToCheck.startsWith('https://')) {
    console.log('\n=== STEP 3: HTTPS CERTIFICATE CHECK ===');
    try {
      console.log('Checking HTTPS certificate...');
      
      const url = new URL(urlToCheck);
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname + url.search,
        method: 'HEAD',
        timeout: 10000,
        rejectUnauthorized: false, // Don't reject invalid certificates
      };
      
      const certInfo = await new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
          const cert = res.socket.getPeerCertificate();
          resolve({
            valid: res.socket.authorized,
            cert
          });
        });
        
        req.on('error', (error) => {
          reject(error);
        });
        
        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Certificate check timed out'));
        });
        
        req.end();
      });
      
      if (certInfo.valid) {
        console.log('Certificate is valid!');
      } else {
        console.log('Certificate is NOT valid!');
      }
      
      if (certInfo.cert) {
        console.log('\nCertificate details:');
        console.log(`Subject: ${certInfo.cert.subject ? JSON.stringify(certInfo.cert.subject) : 'N/A'}`);
        console.log(`Issuer: ${certInfo.cert.issuer ? JSON.stringify(certInfo.cert.issuer) : 'N/A'}`);
        console.log(`Valid from: ${certInfo.cert.valid_from || 'N/A'}`);
        console.log(`Valid to: ${certInfo.cert.valid_to || 'N/A'}`);
        
        // Check if certificate is expired
        if (certInfo.cert.valid_to) {
          const expiryDate = new Date(certInfo.cert.valid_to);
          const now = new Date();
          
          if (expiryDate < now) {
            console.log('Certificate is EXPIRED!');
          } else {
            const daysRemaining = Math.floor((expiryDate - now) / (1000 * 60 * 60 * 24));
            console.log(`Certificate expires in ${daysRemaining} days.`);
          }
        }
      } else {
        console.log('Could not get certificate details.');
      }
    } catch (error) {
      console.log(`Certificate check failed: ${error.message}`);
    }
  }
  
  // Step 4: Detailed HTTP request with different options
  console.log('\n=== STEP 4: DETAILED HTTP REQUEST ===');
  try {
    console.log('Sending detailed HTTP request with different options...');
    
    // Try with different user agent
    console.log('\nTrying with a different User-Agent...');
    const startTime1 = Date.now();
    
    const response1 = await axios.get(urlToCheck, {
      timeout: 30000,
      validateStatus: null,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)'
      }
    }).catch(error => ({ error }));
    
    const responseTime1 = Date.now() - startTime1;
    
    if (response1.error) {
      console.log(`Request failed: ${response1.error.message}`);
    } else {
      console.log(`Response received in ${responseTime1}ms with status ${response1.status}`);
    }
    
    // Try with no redirects
    console.log('\nTrying with no redirects...');
    const startTime2 = Date.now();
    
    const response2 = await axios.get(urlToCheck, {
      timeout: 30000,
      validateStatus: null,
      maxRedirects: 0,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    }).catch(error => ({ error }));
    
    const responseTime2 = Date.now() - startTime2;
    
    if (response2.error) {
      console.log(`Request failed: ${response2.error.message}`);
    } else {
      console.log(`Response received in ${responseTime2}ms with status ${response2.status}`);
    }
    
    // Try with longer timeout
    console.log('\nTrying with longer timeout (60s)...');
    const startTime3 = Date.now();
    
    const response3 = await axios.get(urlToCheck, {
      timeout: 60000,
      validateStatus: null,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    }).catch(error => ({ error }));
    
    const responseTime3 = Date.now() - startTime3;
    
    if (response3.error) {
      console.log(`Request failed: ${response3.error.message}`);
    } else {
      console.log(`Response received in ${responseTime3}ms with status ${response3.status}`);
    }
  } catch (error) {
    console.log(`Detailed HTTP request failed: ${error.message}`);
  }
  
  console.log('\n=== DIAGNOSTIC COMPLETE ===');
  
  // Provide recommendations
  console.log('\nRecommendations:');
  console.log('1. Check if the website is accessible from your server');
  console.log('2. Verify that the SSL certificate is valid and not expired');
  console.log('3. Try increasing the timeout value in your monitor configuration');
  console.log('4. Check if the website is blocking certain user agents or IP addresses');
  console.log('5. Verify that the website is not redirecting to a different URL');
}

checkWebsite().catch(error => {
  console.error('Unhandled error:', error);
});
