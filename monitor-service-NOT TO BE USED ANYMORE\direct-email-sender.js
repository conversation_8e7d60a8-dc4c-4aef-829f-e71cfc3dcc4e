// Direct Email Sender
// This script sends email notifications directly without using an Edge Function
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'email-sender.log');

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Function to send email notification
async function sendEmailNotification(monitorId, status, companyId) {
  try {
    // Check if email alerts are enabled - must be explicitly set to 'true'
    const enableEmailAlerts = process.env.ENABLE_EMAIL_ALERTS === 'true';

    if (!enableEmailAlerts) {
      log(`Email alerts are disabled in .env (ENABLE_EMAIL_ALERTS=${process.env.ENABLE_EMAIL_ALERTS}). Skipping email notification.`, 'INFO');
      return true; // Return true to indicate "success" (as in, we did what was configured)
    }

    // Double-check to make sure we're not sending emails when disabled
    if (process.env.ENABLE_EMAIL_ALERTS !== 'true') {
      log(`Email alerts are not explicitly enabled (ENABLE_EMAIL_ALERTS=${process.env.ENABLE_EMAIL_ALERTS}). Skipping email notification.`, 'INFO');
      return true;
    }

    log(`Preparing to send email notification for monitor ${monitorId} (${status}) to company ${companyId}`);

    // Get monitor details
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('name, target, type')
      .eq('id', monitorId)
      .single();

    if (monitorError) {
      throw new Error(`Failed to get monitor details: ${monitorError.message}`);
    }

    // Get company details
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('name')
      .eq('id', companyId)
      .single();

    if (companyError) {
      throw new Error(`Failed to get company details: ${companyError.message}`);
    }

    // Get company admin emails directly from the database
    const { data: adminData, error: adminEmailsError } = await supabase
      .from('company_members')
      .select('user_id')
      .eq('company_id', companyId)
      .eq('role_type', 'admin');

    if (adminEmailsError) {
      throw new Error(`Failed to get admin emails: ${adminEmailsError.message}`);
    }

    // Get the email addresses for these users
    const userIds = adminData.map(item => item.user_id);

    if (userIds.length === 0) {
      log(`No admin users found for company ${companyId}`, 'WARN');
      return false;
    }

    // Get emails from auth.users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .in('id', userIds);

    if (userError) {
      throw new Error(`Failed to get user emails: ${userError.message}`);
    }

    // Extract email addresses
    const adminEmails = userData.map(user => user.email).filter(Boolean);

    // Get all superadmin emails
    const { data: superadminEmails, error: superadminError } = await supabase
      .rpc('get_all_superadmin_emails');

    if (superadminError) {
      log(`Error getting superadmin emails: ${superadminError.message}`, 'WARN');
    } else if (superadminEmails && superadminEmails.length > 0) {
      // Add superadmin emails to the recipients list
      log(`Found ${superadminEmails.length} superadmin emails to notify`);
      adminEmails.push(...superadminEmails);
    }

    // Remove duplicates from the email list
    const uniqueEmails = [...new Set(adminEmails)];

    if (!uniqueEmails || uniqueEmails.length === 0) {
      log(`No admin or superadmin emails found for company ${companyId}`, 'WARN');
      return false;
    }

    log(`Found ${uniqueEmails.length} total emails to notify (company admins + superadmins)`);

    // Create email content
    const statusText = status === 'up' ? 'UP' : status === 'down' ? 'DOWN' : 'DEGRADED';
    const statusColor = status === 'up' ? '#4CAF50' : status === 'down' ? '#F44336' : '#FF9800';

    const subject = `[${company.name}] Monitor ${monitor.name} is ${statusText}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Monitor Status Change</h2>
        <div style="padding: 20px; border-radius: 5px; margin-bottom: 20px; background-color: ${statusColor}; color: white;">
          <h3 style="margin-top: 0;">Monitor is now ${statusText}</h3>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <p><strong>Company:</strong> ${company.name}</p>
          <p><strong>Monitor:</strong> ${monitor.name}</p>
          <p><strong>Type:</strong> ${monitor.type}</p>
          <p><strong>Target:</strong> ${monitor.target}</p>
          <p><strong>Status:</strong> <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
        <div style="font-size: 12px; color: #666; margin-top: 30px;">
          <p>This is an automated message from Vurbis Uptime Monitor.</p>
        </div>
      </div>
    `;

    // Send email using Resend API
    // You'll need to set RESEND_API_KEY in your .env file
    const RESEND_API_KEY = process.env.RESEND_API_KEY;

    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not set in .env file');
    }

    log(`Sending email to ${uniqueEmails.join(', ')}`);

    // Get Resend API URL from environment variable or use default
    const RESEND_API_URL = process.env.RESEND_API_URL || 'https://api.resend.com/emails';
    const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || 'Vurbis Uptime Monitor <<EMAIL>>';

    const response = await axios.post(RESEND_API_URL, {
      from: RESEND_FROM_EMAIL,
      to: uniqueEmails,
      subject,
      html,
    }, {
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 200) {
      log(`Email notification sent successfully to ${uniqueEmails.length} recipients (company admins + superadmins)`);
      return true;
    } else {
      throw new Error(`Failed to send email notification: ${response.statusText}`);
    }
  } catch (error) {
    log(`Error sending email notification: ${error.message}`, 'ERROR');
    return false;
  }
}

// Export the function for use in other modules
module.exports = { sendEmailNotification };

// If this script is run directly, handle command line arguments
if (require.main === module) {
  // Parse command line arguments
  const args = process.argv.slice(2);
  let monitorId = null;
  let status = 'down';
  let companyId = null;

  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--monitor' && i + 1 < args.length) {
      monitorId = args[i + 1];
      i++;
    } else if (args[i] === '--status' && i + 1 < args.length) {
      status = args[i + 1];
      i++;
    } else if (args[i] === '--company' && i + 1 < args.length) {
      companyId = args[i + 1];
      i++;
    }
  }

  if (!monitorId || !companyId) {
    log('Usage: node direct-email-sender.js --monitor <monitor_id> --status <up|down|degraded> --company <company_id>', 'ERROR');
    process.exit(1);
  }

  // Send the email
  sendEmailNotification(monitorId, status, companyId)
    .then(success => {
      if (success) {
        log('Email sent successfully!');
      } else {
        log('Failed to send email.', 'ERROR');
      }
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`Unexpected error: ${error.message}`, 'ERROR');
      process.exit(1);
    });
}
