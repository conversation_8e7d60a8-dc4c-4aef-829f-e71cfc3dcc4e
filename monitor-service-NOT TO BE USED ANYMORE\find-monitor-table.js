// <PERSON>ript to find the correct monitor table name
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Common monitor-related table names
const possibleTableNames = [
  'monitors',
  'monitor',
  'sites',
  'site',
  'endpoints',
  'endpoint',
  'checks',
  'check',
  'urls',
  'url',
  'webpages',
  'webpage',
  'pages',
  'page',
  'services',
  'service'
];

// Common monitor-related column names
const monitorColumns = [
  'url',
  'target',
  'endpoint',
  'site',
  'check',
  'interval',
  'timeout',
  'active',
  'status'
];

async function findMonitorTable() {
  console.log('=== MONITOR TABLE FINDER ===');
  console.log(`Supabase URL: ${SUPABASE_URL}`);
  console.log(`Supabase Key: ${SUPABASE_KEY.substring(0, 10)}...${SUPABASE_KEY.substring(SUPABASE_KEY.length - 5)}`);

  // Try each possible table name
  console.log('\nChecking possible monitor table names...');

  const results = [];

  for (const tableName of possibleTableNames) {
    try {
      console.log(`\nChecking table '${tableName}'...`);

      // Try to select from the table
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(5);

      if (error) {
        console.log(`Error: ${error.message}`);
        continue;
      }

      if (!data || data.length === 0) {
        console.log(`Table '${tableName}' exists but has no records.`);
        continue;
      }

      console.log(`Found ${data.length} records in table '${tableName}'!`);

      // Check if this looks like a monitor table
      const firstRecord = data[0];
      const columns = Object.keys(firstRecord);

      console.log(`Columns: ${columns.join(', ')}`);

      // Check how many monitor-related columns it has
      const matchingColumns = columns.filter(column =>
        monitorColumns.some(monitorColumn =>
          column.toLowerCase().includes(monitorColumn.toLowerCase())
        )
      );

      const score = matchingColumns.length;

      console.log(`Matching columns: ${matchingColumns.join(', ')}`);
      console.log(`Score: ${score}`);

      // Add to results
      results.push({
        tableName,
        score,
        recordCount: data.length,
        columns,
        matchingColumns,
        sampleRecord: firstRecord
      });

      // Print the first record
      console.log('\nSample record:');
      Object.entries(firstRecord).forEach(([key, value]) => {
        console.log(`${key}: ${value === null ? 'null' : value} (${typeof value})`);
      });
    } catch (error) {
      console.log(`Exception: ${error.message}`);
    }
  }

  // Sort results by score (descending)
  results.sort((a, b) => b.score - a.score);

  console.log('\n=== RESULTS ===');

  if (results.length === 0) {
    console.log('No monitor tables found.');
  } else {
    console.log('Potential monitor tables (sorted by likelihood):');

    results.forEach((result, index) => {
      console.log(`\n${index + 1}. Table: ${result.tableName}`);
      console.log(`   Score: ${result.score}`);
      console.log(`   Records: ${result.recordCount}`);
      console.log(`   Matching columns: ${result.matchingColumns.join(', ')}`);
    });

    console.log('\nMost likely monitor table:');
    console.log(`Table name: ${results[0].tableName}`);
    console.log('Sample record:');
    Object.entries(results[0].sampleRecord).forEach(([key, value]) => {
      console.log(`${key}: ${value === null ? 'null' : value} (${typeof value})`);
    });

    console.log('\nTo use this table, update your .env file:');
    console.log(`MONITOR_TABLE=${results[0].tableName}`);
  }

  console.log('\n=== SEARCH COMPLETE ===');
}

findMonitorTable().catch(error => {
  console.error('Unhandled error:', error);
});
