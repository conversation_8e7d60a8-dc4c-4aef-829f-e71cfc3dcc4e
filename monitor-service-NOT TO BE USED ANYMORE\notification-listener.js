// Notification Listener Service
// This service listens for database notifications and triggers email notifications
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');
const { Pool } = require('pg');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'notification-listener.log');

// Database connection info (from Supabase connection string)
const DB_HOST = process.env.DB_HOST;
const DB_PORT = process.env.DB_PORT || 5432;
const DB_NAME = process.env.DB_NAME || 'postgres';
const DB_USER = process.env.DB_USER || 'postgres';
const DB_PASSWORD = process.env.DB_PASSWORD;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY || !DB_HOST || !DB_PASSWORD) {
  console.error('ERROR: Required environment variables are not set.');
  console.error('Please set SUPABASE_URL, SUPABASE_KEY, DB_HOST, and DB_PASSWORD in your .env file.');
  process.exit(1);
}

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Log to console
  console.log(logMessage);

  // Log to file
  fs.appendFileSync(LOG_FILE, logMessage + os.EOL);
}

// Create a PostgreSQL connection pool
const pool = new Pool({
  host: DB_HOST,
  port: DB_PORT,
  database: DB_NAME,
  user: DB_USER,
  password: DB_PASSWORD,
  ssl: true
});

// Function to send email notification
async function sendEmailNotification(payload) {
  try {
    log(`Sending email notification for monitor ${payload.monitor_id} (${payload.status}) to company ${payload.company_id}`);

    // Call the Supabase Edge Function to send the email
    const response = await axios.post(
      `${SUPABASE_URL}/functions/v1/send-monitor-notification`,
      payload,
      {
        headers: {
          'Authorization': `Bearer ${SUPABASE_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 200) {
      log(`Email notification sent successfully for monitor ${payload.monitor_id} to company ${payload.company_id}`);
      return true;
    } else {
      throw new Error(`Failed to send email notification: ${response.statusText}`);
    }
  } catch (error) {
    log(`Error sending email notification: ${error.message}`, 'ERROR');
    return false;
  }
}

// Start the notification listener
async function startListener() {
  log('Starting notification listener service...');

  try {
    // Test Supabase connection
    const { data, error, count } = await supabase
      .from('monitors')
      .select('*', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    log(`Connected to Supabase. Found ${count} monitors.`);

    // Connect to the database for notifications
    const client = await pool.connect();

    // Listen for notifications
    await client.query('LISTEN monitor_status_change');

    log('Listening for monitor status change notifications...');

    // Set up notification handler
    client.on('notification', async (notification) => {
      try {
        // Parse the payload
        const payload = JSON.parse(notification.payload);
        log(`Received notification: ${JSON.stringify(payload)}`);

        // Send email notification
        await sendEmailNotification(payload);
      } catch (error) {
        log(`Error processing notification: ${error.message}`, 'ERROR');
      }
    });

    // Keep the connection alive
    setInterval(() => {
      client.query('SELECT 1');
    }, 60000);

    // Handle errors
    client.on('error', (err) => {
      log(`Database connection error: ${err.message}`, 'ERROR');
      setTimeout(startListener, 10000); // Restart after 10 seconds
    });

    log('Notification listener service started successfully');
  } catch (error) {
    log(`Failed to start service: ${error.message}`, 'ERROR');
    log('Retrying in 30 seconds...');

    // Retry after a delay
    setTimeout(startListener, 30000);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('Shutting down notification listener service...');
  pool.end();
  log('Notification listener service stopped');
  process.exit(0);
});

// Start the service
startListener();
