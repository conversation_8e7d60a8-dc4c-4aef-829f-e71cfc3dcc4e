// Script to query monitors using raw SQL
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function queryMonitors() {
  console.log('Querying monitors using raw SQL...');

  try {
    // Query all monitors
    const { data, error } = await supabase.rpc('execute_sql', {
      query_text: `
        SELECT
          id,
          name,
          target,
          type,
          interval,
          timeout,
          active,
          pg_typeof(active) as active_type,
          created_at,
          company_id,
          user_id
        FROM
          monitors
      `
    });

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      console.log('No monitors found in the database.');
      return;
    }

    console.log(`Found ${data.length} total monitors in the database.\n`);

    // Display all monitors
    console.log('=== MONITORS FROM SQL QUERY ===');
    data.forEach((monitor, index) => {
      console.log(`\nMonitor #${index + 1}:`);
      console.log('----------------------------------------');

      // Display all properties
      Object.entries(monitor).forEach(([key, value]) => {
        console.log(`${key}: ${value === null ? 'null' : value}`);
      });

      console.log('----------------------------------------');
    });

    // Try to fix active status using SQL
    console.log('\nDo you want to set all monitors to active=true using SQL? (y/n)');
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });

    readline.question('> ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        try {
          const { data: updateData, error: updateError } = await supabase.rpc('execute_sql', {
            query_text: `
              UPDATE monitors SET active = true;
              SELECT 'Updated ' || count(*) || ' monitors' as result FROM monitors;
            `
          });

          if (updateError) {
            console.error('Error updating monitors:', updateError.message);
          } else {
            console.log('Update result:', updateData);
            console.log('Successfully set all monitors to active=true!');
          }
        } catch (e) {
          console.error('Error:', e.message);
        }
      }

      readline.close();
    });

  } catch (error) {
    console.error('Error:', error.message);

    // Try alternative approach
    console.log('\nTrying alternative approach...');
    try {
      const { data, error } = await supabase
        .from('monitors')
        .select('*');

      if (error) {
        throw error;
      }

      console.log(`Found ${data.length} monitors using standard query.`);
      console.log('First monitor:', data[0]);
    } catch (e) {
      console.error('Alternative approach failed:', e.message);
    }
  }
}

queryMonitors();
