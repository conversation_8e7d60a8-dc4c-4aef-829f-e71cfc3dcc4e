// Script to test the "Check Now" functionality
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Function to check a monitor immediately
async function checkMonitorNow(monitorId) {
  try {
    // Get the monitor details
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('*')
      .eq('id', monitorId)
      .single();

    if (monitorError) {
      throw new Error(`Failed to get monitor: ${monitorError.message}`);
    }

    if (!monitor) {
      throw new Error(`Monitor with ID ${monitorId} not found`);
    }

    console.log(`Checking monitor: ${monitor.name} (${monitor.target})`);

    // Perform the check
    let status = false;
    let responseTime = null;
    let errorMessage = null;
    const startTime = Date.now();

    try {
      const axios = require('axios');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000);

      try {
        const response = await axios.get(monitor.target, {
          signal: controller.signal,
          timeout: monitor.timeout * 1000,
          validateStatus: null // Don't throw on non-2xx responses
        });

        clearTimeout(timeoutId);
        status = response.status >= 200 && response.status < 300;
        responseTime = Date.now() - startTime;

        if (!status) {
          errorMessage = `HTTP status: ${response.status}`;
        }
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    } catch (error) {
      status = false;
      errorMessage = `Error: ${error.message}`;
      console.error(`Check failed for monitor ${monitor.name}: ${error.message}`);
    }

    // Save the check result
    const checkResult = {
      monitor_id: monitor.id,
      status,
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };

    const { error: insertError } = await supabase
      .from('monitor_history')
      .insert(checkResult);

    if (insertError) {
      throw new Error(`Failed to save check result: ${insertError.message}`);
    }

    console.log(`Check result for ${monitor.name}: ${status ? 'UP' : 'DOWN'} (${responseTime}ms)`);
    
    return {
      monitor_id: monitor.id,
      name: monitor.name,
      status,
      response_time: responseTime,
      error_message: errorMessage
    };
  } catch (error) {
    console.error(`Error checking monitor: ${error.message}`);
    throw error;
  }
}

// Function to check all active monitors
async function checkAllMonitorsNow() {
  try {
    // Get all active monitors
    const { data: monitors, error: monitorsError } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (monitorsError) {
      throw new Error(`Failed to get monitors: ${monitorsError.message}`);
    }

    if (!monitors || monitors.length === 0) {
      console.log('No active monitors found');
      return [];
    }

    console.log(`Found ${monitors.length} active monitors`);

    // Check each monitor
    const results = [];
    for (const monitor of monitors) {
      try {
        const result = await checkMonitorNow(monitor.id);
        results.push(result);
      } catch (error) {
        console.error(`Error checking monitor ${monitor.name}: ${error.message}`);
      }
    }

    console.log(`Successfully checked ${results.length} monitors`);
    return results;
  } catch (error) {
    console.error(`Error checking monitors: ${error.message}`);
    throw error;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const monitorId = args.find(arg => arg.startsWith('--id='))?.split('=')[1];

// Run the appropriate function
if (monitorId) {
  console.log(`Checking monitor with ID: ${monitorId}`);
  checkMonitorNow(monitorId)
    .then(result => {
      console.log('Check completed successfully:');
      console.log(result);
      process.exit(0);
    })
    .catch(error => {
      console.error(`Error: ${error.message}`);
      process.exit(1);
    });
} else {
  console.log('Checking all active monitors');
  checkAllMonitorsNow()
    .then(results => {
      console.log('All checks completed successfully:');
      console.log(`Successfully checked ${results.length} monitors`);
      process.exit(0);
    })
    .catch(error => {
      console.error(`Error: ${error.message}`);
      process.exit(1);
    });
}
