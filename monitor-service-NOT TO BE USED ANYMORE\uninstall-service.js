// Script to uninstall the monitor service
const { Service } = require('node-windows');
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'VUM Monitor Service',
  script: path.join(__dirname, 'concurrent-monitor-service.js')
});

// Listen for the "uninstall" event
svc.on('uninstall', () => {
  console.log('Service uninstalled successfully!');
});

// Listen for the "error" event
svc.on('error', (err) => {
  console.error('Error uninstalling service:', err);
});

// Uninstall the service
console.log('Uninstalling service...');
svc.uninstall();
