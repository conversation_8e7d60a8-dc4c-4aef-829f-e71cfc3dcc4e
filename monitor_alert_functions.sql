-- Function to get monitors with consecutive failures
CREATE OR REPLACE FUNCTION get_consecutive_failures(failure_threshold INTEGER)
RETURNS TABLE (
    id UUID,
    name TEXT,
    last_check TIMESTAMPTZ,
    consecutive_failures INTEGER,
    last_error TEXT,
    alert_email TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH consecutive_fails AS (
        SELECT 
            monitor_id,
            COUNT(*) as fail_count
        FROM (
            SELECT 
                monitor_id,
                status,
                timestamp,
                LAG(status) OVER (PARTITION BY monitor_id ORDER BY timestamp DESC) as prev_status
            FROM monitor_history
            WHERE timestamp > NOW() - INTERVAL '24 hours'
        ) recent_history
        WHERE status = false AND (prev_status = false OR prev_status IS NULL)
        GROUP BY monitor_id
    )
    SELECT 
        m.id,
        m.name,
        mh.timestamp as last_check,
        cf.fail_count as consecutive_failures,
        mh.error_message as last_error,
        m.alert_email
    FROM monitors m
    JOIN consecutive_fails cf ON cf.monitor_id = m.id
    JOIN monitor_history mh ON mh.monitor_id = m.id
    WHERE 
        cf.fail_count >= failure_threshold
        AND mh.timestamp = (
            SELECT MAX(timestamp)
            FROM monitor_history
            WHERE monitor_id = m.id
        );
END;
$$;

-- Function to get monitors with high response times
CREATE OR REPLACE FUNCTION get_slow_monitors(response_threshold INTEGER)
RETURNS TABLE (
    id UUID,
    name TEXT,
    last_check TIMESTAMPTZ,
    response_time INTEGER,
    alert_email TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH avg_response_times AS (
        SELECT 
            monitor_id,
            AVG(response_time) as avg_time
        FROM monitor_history
        WHERE 
            timestamp > NOW() - INTERVAL '1 hour'
            AND response_time IS NOT NULL
        GROUP BY monitor_id
        HAVING AVG(response_time) > response_threshold
    )
    SELECT 
        m.id,
        m.name,
        mh.timestamp as last_check,
        mh.response_time,
        m.alert_email
    FROM monitors m
    JOIN avg_response_times art ON art.monitor_id = m.id
    JOIN monitor_history mh ON mh.monitor_id = m.id
    WHERE mh.timestamp = (
        SELECT MAX(timestamp)
        FROM monitor_history
        WHERE monitor_id = m.id
    );
END;
$$;

-- Create alert_logs table
CREATE TABLE IF NOT EXISTS alert_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    failed_monitors INTEGER NOT NULL,
    slow_monitors INTEGER NOT NULL
);

-- Add alert_email column to monitors table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'monitors' AND column_name = 'alert_email'
    ) THEN
        ALTER TABLE monitors ADD COLUMN alert_email TEXT;
    END IF;
END $$;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_monitor_history_timestamp_status 
ON monitor_history(timestamp, status);

-- Create view for monitoring dashboard
CREATE OR REPLACE VIEW monitor_status_summary AS
WITH latest_checks AS (
    SELECT DISTINCT ON (monitor_id)
        monitor_id,
        status,
        response_time,
        timestamp
    FROM monitor_history
    ORDER BY monitor_id, timestamp DESC
),
hourly_stats AS (
    SELECT
        monitor_id,
        DATE_TRUNC('hour', timestamp) as hour,
        COUNT(*) as total_checks,
        COUNT(*) FILTER (WHERE status = true) as successful_checks,
        AVG(response_time) as avg_response_time
    FROM monitor_history
    WHERE timestamp > NOW() - INTERVAL '24 hours'
    GROUP BY monitor_id, DATE_TRUNC('hour', timestamp)
)
SELECT
    m.id,
    m.name,
    m.url,
    lc.status as current_status,
    lc.response_time as last_response_time,
    lc.timestamp as last_check,
    COUNT(DISTINCT hs.hour) as hours_monitored,
    ROUND(AVG(hs.successful_checks * 100.0 / hs.total_checks), 2) as uptime_percentage,
    ROUND(AVG(hs.avg_response_time), 2) as avg_response_time_24h
FROM monitors m
LEFT JOIN latest_checks lc ON lc.monitor_id = m.id
LEFT JOIN hourly_stats hs ON hs.monitor_id = m.id
GROUP BY m.id, m.name, m.url, lc.status, lc.response_time, lc.timestamp;
