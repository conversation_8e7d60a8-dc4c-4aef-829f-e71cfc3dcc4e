# Add Health Endpoint to VUM Backend Service

## Context
The VUM Backend monitoring service currently runs with `npm start` (which executes `concurrent-monitor-service.js`) but doesn't expose any HTTP endpoints for health checking. The frontend needs to be able to ping the VUM Backend service to verify it's running and healthy.

## Current Architecture
- **Frontend**: Calls Supabase Edge Functions for manual checks
- **VUM Backend Service**: Runs independently, connects to Supabase for scheduled monitoring
- **Supabase**: Always in the cloud, same instance regardless of where VUM Backend runs

## Goal
Add a simple HTTP server with health endpoints to the VUM Backend service so the frontend can check if the service is running.

## Requirements

### 1. Add HTTP Server to concurrent-monitor-service.js
- Add Express.js server that runs alongside the monitoring service
- Default port: 3001 (configurable via environment variable)
- Enable CORS for frontend access

### 2. Health Endpoints Needed
```javascript
// Basic health check
GET /health
Response: {
  "status": "healthy",
  "service": "VUM Backend Service", 
  "timestamp": "2025-01-20T10:30:00.000Z",
  "uptime": 3600.5,
  "version": "1.0.0"
}

// Detailed status (optional)
GET /status
Response: {
  "status": "healthy",
  "service": "VUM Backend Service",
  "timestamp": "2025-01-20T10:30:00.000Z", 
  "uptime": 3600.5,
  "version": "1.0.0",
  "monitoring": {
    "active": true,
    "lastCheck": "2025-01-20T10:29:45.000Z",
    "totalChecks": 1234
  },
  "database": {
    "connected": true,
    "lastQuery": "2025-01-20T10:29:45.000Z"
  }
}
```

### 3. Implementation Details
- Use Express.js (already in dependencies)
- Add CORS middleware
- Keep the HTTP server lightweight - don't interfere with monitoring
- Log when HTTP server starts
- Handle graceful shutdown
- Make port configurable via environment variable `HEALTH_CHECK_PORT` (default: 3001)

### 4. Environment Variable
Add to .env file:
```
# Health Check Server Configuration
HEALTH_CHECK_PORT=3001
```

### 5. Error Handling
- If HTTP server fails to start, log error but continue monitoring
- Return appropriate HTTP status codes
- Handle server shutdown gracefully

### 6. Integration Points
- Start HTTP server after Supabase connection is established
- Include monitoring service status in health response
- Ensure HTTP server doesn't block the monitoring loop

## Expected Behavior After Implementation
1. VUM Backend service starts with `npm start`
2. Monitoring service runs as before
3. HTTP server starts on port 3001
4. Frontend can ping `http://*************:3001/health` to check if service is running
5. Health endpoint returns JSON with service status

## Files to Modify
- `concurrent-monitor-service.js` - Add HTTP server
- `.env` - Add HEALTH_CHECK_PORT configuration
- `package.json` - Ensure Express.js dependency exists

## Testing
After implementation, test with:
```bash
curl http://localhost:3001/health
curl http://*************:3001/health  # for external server
```

Should return JSON response with status "healthy" and service information.

## Notes
- Keep the HTTP server simple and lightweight
- Don't expose sensitive information in health endpoints
- Ensure CORS is enabled for frontend access
- The monitoring functionality should remain unchanged
- HTTP server is purely for health checking, not for monitor operations
