import dotenv from 'dotenv';
import fs from 'fs/promises';
import { createClient } from '@supabase/supabase-js';
import path from 'path';

dotenv.config();

const SUPABASE_PROJECT_REF = process.env.SUPABASE_PROJECT_REF;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;

if (!SUPABASE_PROJECT_REF || !SUPABASE_SERVICE_KEY) {
  console.error('Missing required environment variables');
  process.exit(1);
}

async function rotateKeys() {
  try {
    console.log('Starting key rotation...');

    // 1. Create new API key using Supabase Management API
    const SUPABASE_MANAGEMENT_API = process.env.SUPABASE_MANAGEMENT_API;

    if (!SUPABASE_MANAGEMENT_API) {
      console.error('SUPABASE_MANAGEMENT_API environment variable is not set');
      process.exit(1);
    }
    const managementClient = createClient(
      SUPABASE_MANAGEMENT_API,
      SUPABASE_SERVICE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Generate new service role key
    const { data: newKey, error: keyError } = await managementClient
      .rpc('generate_service_key', {
        project_ref: SUPABASE_PROJECT_REF,
        name: `service_key_${new Date().toISOString().split('T')[0]}`
      });

    if (keyError) {
      throw new Error(`Failed to generate new key: ${keyError.message}`);
    }

    // 2. Update .env file with new key
    const envPath = path.join(process.cwd(), '.env');
    const envContent = await fs.readFile(envPath, 'utf-8');

    // Create backup of current .env
    await fs.writeFile(`${envPath}.backup`, envContent);

    // Update the service key in .env
    const updatedContent = envContent.replace(
      /SUPABASE_SERVICE_KEY=.*/,
      `SUPABASE_SERVICE_KEY=${newKey.key}`
    );

    await fs.writeFile(envPath, updatedContent);

    // 3. Test the new key
    const testClient = createClient(
      process.env.SUPABASE_URL,
      newKey.key,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const { data: testData, error: testError } = await testClient
      .from('monitors')
      .select('count(*)', { count: 'exact' })
      .limit(1);

    if (testError) {
      // Rollback if test fails
      await fs.copyFile(`${envPath}.backup`, envPath);
      throw new Error(`New key validation failed: ${testError.message}`);
    }

    // 4. Clean up old backup if everything succeeded
    await fs.unlink(`${envPath}.backup`);

    console.log('Key rotation completed successfully!');
    console.log('Next rotation scheduled for 30 days from now');

  } catch (error) {
    console.error('Error during key rotation:', error.message);

    // Try to restore backup if it exists
    try {
      const backupPath = `${path.join(process.cwd(), '.env')}.backup`;
      await fs.access(backupPath);
      await fs.copyFile(backupPath, path.join(process.cwd(), '.env'));
      console.log('Restored previous .env from backup');
    } catch (backupError) {
      console.error('Could not restore backup:', backupError.message);
    }

    process.exit(1);
  }
}

// Run key rotation
rotateKeys();
