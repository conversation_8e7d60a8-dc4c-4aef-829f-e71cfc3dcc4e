/**
 * Credential Check Script
 *
 * This script scans the codebase for potentially hardcoded credentials
 * and warns about files that might contain sensitive information.
 *
 * Usage: node scripts/check-credentials.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const projectRoot = path.resolve(__dirname, '..');
dotenv.config({ path: path.join(projectRoot, '.env') });

// Function to build credential patterns from environment variables
function buildCredentialPatterns() {
  const patterns = [
    /api[_-]?key/i,
    /auth[_-]?token/i,
    /password/i,
    /secret/i,
    /credential/i,
    /supabase[_-]?key/i,
    /supabase[_-]?url/i,
    /https:\/\/[a-zA-Z0-9-]+\.supabase\.co/i, // Supabase URL pattern
  ];

  // Add patterns for actual environment variable values if they exist
  const sensitiveEnvVars = [
    'SUPABASE_URL',
    'SUPABASE_KEY',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_KEY',
    'RESEND_API_KEY',
    'DB_PASSWORD'
  ];

  for (const varName of sensitiveEnvVars) {
    const value = process.env[varName];
    if (value && value.length > 8) { // Only use values that are reasonably long
      // Escape special regex characters and create a pattern
      const escapedValue = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const pattern = new RegExp(escapedValue, 'i');
      patterns.push(pattern);
    }
  }

  return patterns;
}

// Get patterns based on environment variables
const CREDENTIAL_PATTERNS = buildCredentialPatterns();

// Files and directories to ignore
const IGNORE_PATHS = [
  'node_modules',
  'dist',
  '.git',
  '.env.example',
  '.env.local.example',
  'scripts/check-credentials.js',
  'docs',
  // Allow fallback values in the Supabase client for development
  'src/integrations/supabase/client.ts',
];

// File extensions to check
const CHECK_EXTENSIONS = [
  '.js',
  '.jsx',
  '.ts',
  '.tsx',
  '.json',
  '.md',
  '.html',
  '.css',
];

// Function to check if a file might contain credentials
function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const matches = [];

    CREDENTIAL_PATTERNS.forEach(pattern => {
      if (pattern.test(content)) {
        matches.push(pattern.toString().replace(/\//g, ''));
      }
    });

    if (matches.length > 0) {
      console.log(`⚠️  ${filePath} might contain credentials (matched patterns: ${matches.join(', ')})`);
      return true;
    }
  } catch (error) {
    console.error(`Error reading file ${filePath}: ${error.message}`);
  }
  return false;
}

// Function to recursively scan directories
function scanDirectory(dir, results = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);

    // Skip ignored paths
    if (IGNORE_PATHS.some(ignorePath => filePath.includes(ignorePath))) {
      return;
    }

    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      scanDirectory(filePath, results);
    } else {
      const ext = path.extname(filePath);
      if (CHECK_EXTENSIONS.includes(ext)) {
        if (checkFile(filePath)) {
          results.push(filePath);
        }
      }
    }
  });

  return results;
}

// Main function
function main() {
  console.log('🔍 Scanning codebase for potential hardcoded credentials...');

  const rootDir = path.resolve(__dirname, '..');
  const results = scanDirectory(rootDir);

  if (results.length === 0) {
    console.log('✅ No potential hardcoded credentials found!');
  } else {
    console.log(`\n⚠️  Found ${results.length} files that might contain hardcoded credentials.`);
    console.log('Please review these files and ensure all sensitive information is stored in environment variables.');
    console.log('\nRemember:');
    console.log('1. NEVER hardcode sensitive information in your code');
    console.log('2. NEVER commit .env.local or any file containing actual credentials to the repository');
    console.log('3. Always use environment variables for sensitive information');
  }
}

main();
