/**
 * Environment Setup Script
 *
 * This script helps developers set up their environment by:
 * 1. Checking if .env.local exists
 * 2. Creating .env.local from .env.local.example if it doesn't exist
 * 3. Prompting for required environment variables
 *
 * IMPORTANT SECURITY NOTICE:
 * - NEVER hardcode sensitive information like API keys, passwords, or credentials in your code
 * - NEVER commit .env.local or any file containing actual credentials to the repository
 * - Always use environment variables for sensitive information
 * - The .env.local file is listed in .gitignore to prevent it from being committed
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Paths
const rootDir = path.resolve(__dirname, '..');
const envLocalPath = path.join(rootDir, '.env.local');
const envLocalExamplePath = path.join(rootDir, '.env.local.example');

// Check if .env.local exists
if (fs.existsSync(envLocalPath)) {
  console.log('✅ .env.local file already exists');
  rl.close();
} else {
  console.log('❌ .env.local file does not exist');
  console.log('Creating .env.local from .env.local.example...');

  // Read .env.local.example
  const envLocalExample = fs.readFileSync(envLocalExamplePath, 'utf8');

  // Create .env.local with the same content
  fs.writeFileSync(envLocalPath, envLocalExample);

  console.log('✅ .env.local file created');
  console.log('Please update the values in .env.local with your own values');

  // Prompt for required environment variables
  console.log('\nRequired environment variables:');
  console.log('- VITE_SUPABASE_URL: The URL of your Supabase project');
  console.log('- VITE_SUPABASE_ANON_KEY: The anonymous key for your Supabase project');

  rl.question('\nDo you want to enter these values now? (y/n) ', (answer) => {
    if (answer.toLowerCase() === 'y') {
      rl.question('VITE_SUPABASE_URL: ', (supabaseUrl) => {
        rl.question('VITE_SUPABASE_ANON_KEY: ', (supabaseAnonKey) => {
          // Read the current .env.local
          let envLocal = fs.readFileSync(envLocalPath, 'utf8');

          // Replace the values
          envLocal = envLocal.replace(/VITE_SUPABASE_URL=.*/, `VITE_SUPABASE_URL=${supabaseUrl}`);
          envLocal = envLocal.replace(/VITE_SUPABASE_ANON_KEY=.*/, `VITE_SUPABASE_ANON_KEY=${supabaseAnonKey}`);

          // Write the updated .env.local
          fs.writeFileSync(envLocalPath, envLocal);

          console.log('✅ .env.local file updated');
          rl.close();
        });
      });
    } else {
      console.log('Please update the values in .env.local manually');
      rl.close();
    }
  });
}

rl.on('close', () => {
  console.log('\nEnvironment setup complete!');
  console.log('You can now run the application with:');
  console.log('npm run dev');
});
