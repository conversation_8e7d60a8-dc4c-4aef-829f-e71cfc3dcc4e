import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, from, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { supabase } from '../../shared/supabase/supabase.client';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private userSubject = new BehaviorSubject<any>(null);
  private sessionSubject = new BehaviorSubject<any>(null);
  private loadingSubject = new BehaviorSubject<boolean>(true);

  user$ = this.userSubject.asObservable();
  session$ = this.sessionSubject.asObservable();
  loading$ = this.loadingSubject.asObservable();

  constructor(
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    // Initialize auth state
    this.initializeAuthState();

    // Listen for auth changes
    supabase.auth.onAuthStateChange((event, session) => {
      this.sessionSubject.next(session);
      this.userSubject.next(session?.user ?? null);
      this.loadingSubject.next(false);
    });
  }

  private async initializeAuthState() {
    try {
      const { data } = await supabase.auth.getSession();
      this.sessionSubject.next(data.session);
      this.userSubject.next(data.session?.user ?? null);
    } catch (error) {
      console.error('Error initializing auth state:', error);
    } finally {
      this.loadingSubject.next(false);
    }
  }

  signIn(email: string, password: string): Observable<any> {
    return from(supabase.auth.signInWithPassword({ email, password }))
      .pipe(
        tap(({ data, error }) => {
          if (error) {
            this.showErrorMessage('Login failed', error.message);
            throw error;
          } else {
            this.showSuccessMessage('Login successful', 'Welcome back!');
          }
        }),
        map(({ data }) => data),
        catchError(error => {
          console.error('Login error:', error);
          return of(null);
        })
      );
  }

  signUp(email: string, password: string, fullName: string): Observable<any> {
    return from(supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        }
      }
    }))
      .pipe(
        tap(async ({ data, error }) => {
          if (error) {
            this.showErrorMessage('Registration failed', error.message);
            throw error;
          } else {
            // Create user profile in the users table
            if (data.user) {
              try {
                const { error: profileError } = await supabase
                  .from('users')
                  .insert({
                    id: data.user.id,
                    email: email,
                    full_name: fullName,
                  });

                if (profileError) {
                  console.warn("Profile creation warning:", profileError);
                  this.showInfoMessage('Note', 'User created in Auth, but profile data may need to be synced.');
                } else {
                  console.log("User profile created successfully in users table");
                }
              } catch (profileError) {
                console.warn("Profile creation error:", profileError);
              }
            }
            this.showSuccessMessage('Registration successful', 'Your account has been created!');
          }
        }),
        map(({ data }) => data),
        catchError(error => {
          console.error('Registration error:', error);
          return of(null);
        })
      );
  }

  signOut(): Observable<void> {
    return from(supabase.auth.signOut())
      .pipe(
        tap(() => {
          this.showSuccessMessage('Logged out', 'You have been successfully logged out.');
          this.router.navigate(['/login']);
        }),
        catchError(error => {
          this.showErrorMessage('Logout failed', 'An unexpected error occurred');
          console.error('Logout error:', error);
          return of(void 0);
        })
      );
  }

  updateProfile(data: { fullName?: string; avatarUrl?: string }): Observable<any> {
    return from(
      (async () => {
        try {
          console.log('Updating user profile with data:', data);

          // Add a timestamp to the avatar URL to prevent caching issues
          let avatarUrl = data.avatarUrl;
          if (avatarUrl) {
            // Remove any existing timestamp parameter
            avatarUrl = avatarUrl.split('?')[0];
            // Add a new timestamp
            avatarUrl = `${avatarUrl}?t=${new Date().getTime()}`;
            console.log('Updated avatar URL with timestamp:', avatarUrl);
          }

          const { data: userData, error } = await supabase.auth.updateUser({
            data: {
              full_name: data.fullName,
              avatar_url: avatarUrl,
            },
          });

          if (error) {
            this.showErrorMessage('Profile update failed', error.message);
            throw error;
          }

          // Update the user profile in the users table if fullName is provided
          if (userData.user) {
            try {
              // First check if user exists in the users table
              const { data: userExists, error: checkError } = await supabase
                .from('users')
                .select('id')
                .eq('id', userData.user.id)
                .single();

              if (checkError || !userExists) {
                // User doesn't exist in users table, create it
                console.log('User not found in users table, creating profile...');

                const { error: insertError } = await supabase
                  .from('users')
                  .insert({
                    id: userData.user.id,
                    email: userData.user.email || '',
                    full_name: data.fullName || userData.user.user_metadata?.full_name || '',
                  });

                if (insertError) {
                  console.warn("Profile creation error:", insertError);
                } else {
                  console.log('User profile created successfully');
                }
              } else if (data.fullName) {
                // User exists, update the full_name
                const { error: profileError } = await supabase
                  .from('users')
                  .update({
                    full_name: data.fullName,
                  })
                  .eq('id', userData.user.id);

                if (profileError) {
                  console.warn("Profile update warning:", profileError);
                } else {
                  console.log('User profile updated successfully');
                }
              }
            } catch (profileError) {
              console.warn("Profile update error:", profileError);
            }
          }

          this.showSuccessMessage('Profile updated', 'Your profile has been updated successfully.');
          return userData.user;
        } catch (error) {
          console.error('Profile update error:', error);
          let errorMessage = "An unexpected error occurred";

          if (error instanceof Error) {
            errorMessage = error.message;
          } else if (typeof error === 'object' && error !== null) {
            errorMessage = JSON.stringify(error);
          }

          this.showErrorMessage('Profile update failed', errorMessage);
          throw error;
        }
      })()
    ).pipe(
      catchError(error => {
        console.error('Profile update observable error:', error);
        return of(null);
      })
    );
  }

  updatePassword(password: string): Observable<any> {
    return from(supabase.auth.updateUser({ password }))
      .pipe(
        tap(({ data, error }) => {
          if (error) {
            this.showErrorMessage('Password update failed', error.message);
            throw error;
          } else {
            this.showSuccessMessage('Password updated', 'Your password has been updated successfully.');
          }
        }),
        map(({ data }) => data),
        catchError(error => {
          console.error('Password update error:', error);
          return of(null);
        })
      );
  }

  isAuthenticated(): Observable<boolean> {
    return this.session$.pipe(
      map(session => !!session)
    );
  }

  private showSuccessMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showErrorMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  private showInfoMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 4000,
      panelClass: ['info-snackbar']
    });
  }
}
