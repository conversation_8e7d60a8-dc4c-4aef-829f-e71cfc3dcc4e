import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, from, of } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { supabase } from '../../shared/supabase/supabase.client';
import { AuthService } from './auth.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class CompanyService {
  private companiesSubject = new BehaviorSubject<any[]>([]);
  private currentCompanySubject = new BehaviorSubject<any>(null);
  private loadingSubject = new BehaviorSubject<boolean>(true);
  private isAdminSubject = new BehaviorSubject<boolean>(false);
  private isSuperadminSubject = new BehaviorSubject<boolean>(false);

  companies$ = this.companiesSubject.asObservable();
  currentCompany$ = this.currentCompanySubject.asObservable();
  loading$ = this.loadingSubject.asObservable();
  isAdmin$ = this.isAdminSubject.asObservable();
  isSuperadmin$ = this.isSuperadminSubject.asObservable();

  constructor(
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    // Load companies when user changes
    this.authService.user$.subscribe(user => {
      if (user) {
        this.loadCompanies();
        this.checkSuperadminStatus();
      } else {
        this.companiesSubject.next([]);
        this.currentCompanySubject.next(null);
        this.isAdminSubject.next(false);
        this.isSuperadminSubject.next(false);
        this.loadingSubject.next(false);
      }
    });
  }

  private async loadCompanies() {
    try {
      this.loadingSubject.next(true);

      // Get user's companies
      const { data: companies, error } = await supabase
        .from('company_members')
        .select(`
          company:companies(id, name, created_at, created_by, deleted, deleted_at, deleted_by, history_retention_days),
          role_type
        `)
        .eq('companies.deleted', false);

      if (error) throw error;

      // Format companies
      const formattedCompanies = companies
        .filter(item => item.company !== null)
        .map(item => ({
          ...item.company,
          role: item.role_type
        }));

      this.companiesSubject.next(formattedCompanies);

      // Set current company if none is selected
      if (!this.currentCompanySubject.value && formattedCompanies.length > 0) {
        this.setCurrentCompany(formattedCompanies[0]);
      }

      // Check admin status for current company
      this.checkAdminStatus();
    } catch (error) {
      console.error('Error loading companies:', error);
      this.showErrorMessage('Error loading companies', error.message || 'An unexpected error occurred');
    } finally {
      this.loadingSubject.next(false);
    }
  }

  private async checkAdminStatus() {
    const currentCompany = this.currentCompanySubject.value;
    if (!currentCompany) {
      this.isAdminSubject.next(false);
      return;
    }

    try {
      const user = await this.authService.user$.pipe(
        map(user => user?.id)
      ).toPromise();

      if (!user) {
        this.isAdminSubject.next(false);
        return;
      }

      // Check if user is admin for this company
      const { data, error } = await supabase
        .from('company_members')
        .select('role_type')
        .eq('company_id', currentCompany.id)
        .eq('user_id', user)
        .single();

      if (error) throw error;

      this.isAdminSubject.next(data?.role_type === 'admin');
    } catch (error) {
      console.error('Error checking admin status:', error);
      this.isAdminSubject.next(false);
    }
  }

  private async checkSuperadminStatus() {
    try {
      const user = await this.authService.user$.pipe(
        map(user => user?.id)
      ).toPromise();

      if (!user) {
        this.isSuperadminSubject.next(false);
        return;
      }

      // Check if user is a superadmin
      const { data, error } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', user)
        .eq('role', 'superadmin')
        .maybeSingle();

      if (error) throw error;

      this.isSuperadminSubject.next(!!data);
    } catch (error) {
      console.error('Error checking superadmin status:', error);
      this.isSuperadminSubject.next(false);
    }
  }

  setCurrentCompany(company: any | null) {
    this.currentCompanySubject.next(company);
    this.checkAdminStatus();
  }

  createCompany(name: string): Observable<any> {
    return this.authService.user$.pipe(
      switchMap(user => {
        if (!user) {
          throw new Error('User not authenticated');
        }

        return from(supabase
          .from('companies')
          .insert({
            name,
            created_by: user.id
          })
          .select()
        );
      }),
      switchMap(async ({ data, error }) => {
        if (error) throw error;
        
        const company = data[0];
        
        // Add the creator as an admin
        const user = await this.authService.user$.pipe(
          map(user => user?.id)
        ).toPromise();
        
        const { error: memberError } = await supabase
          .from('company_members')
          .insert({
            company_id: company.id,
            user_id: user,
            role_type: 'admin'
          });
          
        if (memberError) throw memberError;
        
        // Reload companies
        await this.loadCompanies();
        
        // Set as current company
        this.setCurrentCompany(company);
        
        return company;
      }),
      tap(() => {
        this.showSuccessMessage('Company created', 'Company has been created successfully');
      }),
      catchError(error => {
        console.error('Error creating company:', error);
        this.showErrorMessage('Error creating company', error.message || 'An unexpected error occurred');
        return of(null);
      })
    );
  }

  updateCompany(id: string, data: any): Observable<any> {
    return from(supabase
      .from('companies')
      .update(data)
      .eq('id', id)
      .select()
    ).pipe(
      tap(({ data, error }) => {
        if (error) throw error;
        
        // Reload companies
        this.loadCompanies();
        
        this.showSuccessMessage('Company updated', 'Company has been updated successfully');
      }),
      map(({ data }) => data?.[0] || null),
      catchError(error => {
        console.error('Error updating company:', error);
        this.showErrorMessage('Error updating company', error.message || 'An unexpected error occurred');
        return of(null);
      })
    );
  }

  deleteCompany(id: string): Observable<boolean> {
    return this.authService.user$.pipe(
      switchMap(user => {
        if (!user) {
          throw new Error('User not authenticated');
        }

        return from(supabase
          .from('companies')
          .update({
            deleted: true,
            deleted_at: new Date().toISOString(),
            deleted_by: user.id
          })
          .eq('id', id)
        );
      }),
      tap(({ error }) => {
        if (error) throw error;
        
        // Reload companies
        this.loadCompanies();
        
        // If the deleted company was the current one, reset it
        const currentCompany = this.currentCompanySubject.value;
        if (currentCompany && currentCompany.id === id) {
          this.setCurrentCompany(null);
        }
        
        this.showSuccessMessage('Company deleted', 'Company has been deleted successfully');
      }),
      map(() => true),
      catchError(error => {
        console.error('Error deleting company:', error);
        this.showErrorMessage('Error deleting company', error.message || 'An unexpected error occurred');
        return of(false);
      })
    );
  }

  getCompanyMembers(companyId: string): Observable<any[]> {
    return from(supabase
      .from('company_members')
      .select(`
        id,
        role_type,
        user:users(id, email, full_name, avatar_url)
      `)
      .eq('company_id', companyId)
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data.map(item => ({
          id: item.id,
          role: item.role_type,
          user: item.user
        }));
      }),
      catchError(error => {
        console.error('Error getting company members:', error);
        return of([]);
      })
    );
  }

  addCompanyMember(companyId: string, email: string, role: 'admin' | 'user'): Observable<any> {
    return from(
      (async () => {
        try {
          // First find the user by email
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id')
            .eq('email', email)
            .single();

          if (userError) throw userError;
          if (!userData) throw new Error(`User with email ${email} not found`);

          // Add the user to the company
          const { data, error } = await supabase
            .from('company_members')
            .insert({
              company_id: companyId,
              user_id: userData.id,
              role_type: role
            })
            .select();

          if (error) throw error;
          
          this.showSuccessMessage('Member added', `User ${email} has been added to the company`);
          return data[0];
        } catch (error) {
          console.error('Error adding company member:', error);
          this.showErrorMessage('Error adding member', error.message || 'An unexpected error occurred');
          throw error;
        }
      })()
    ).pipe(
      catchError(error => {
        return of(null);
      })
    );
  }

  updateCompanyMember(memberId: string, role: 'admin' | 'user'): Observable<any> {
    return from(supabase
      .from('company_members')
      .update({ role_type: role })
      .eq('id', memberId)
      .select()
    ).pipe(
      tap(({ data, error }) => {
        if (error) throw error;
        this.showSuccessMessage('Member updated', 'Member role has been updated successfully');
      }),
      map(({ data }) => data?.[0] || null),
      catchError(error => {
        console.error('Error updating company member:', error);
        this.showErrorMessage('Error updating member', error.message || 'An unexpected error occurred');
        return of(null);
      })
    );
  }

  removeCompanyMember(memberId: string): Observable<boolean> {
    return from(supabase
      .from('company_members')
      .delete()
      .eq('id', memberId)
    ).pipe(
      tap(({ error }) => {
        if (error) throw error;
        this.showSuccessMessage('Member removed', 'Member has been removed from the company');
      }),
      map(() => true),
      catchError(error => {
        console.error('Error removing company member:', error);
        this.showErrorMessage('Error removing member', error.message || 'An unexpected error occurred');
        return of(false);
      })
    );
  }

  private showSuccessMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showErrorMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
