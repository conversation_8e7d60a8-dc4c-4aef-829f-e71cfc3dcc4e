import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, from, of } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { supabase } from '../../shared/supabase/supabase.client';
import { AuthService } from './auth.service';
import { CompanyService } from './company.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class MonitorService {
  private monitorsSubject = new BehaviorSubject<any[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(true);

  monitors$ = this.monitorsSubject.asObservable();
  loading$ = this.loadingSubject.asObservable();

  constructor(
    private authService: AuthService,
    private companyService: CompanyService,
    private snackBar: MatSnackBar
  ) {
    // Load monitors when company changes
    this.companyService.currentCompany$.subscribe(company => {
      this.loadMonitors(company?.id);
    });
  }

  private async loadMonitors(companyId?: string) {
    try {
      this.loadingSubject.next(true);

      let query = supabase
        .from('monitors')
        .select('*')
        .eq('deleted', false);

      if (companyId) {
        query = query.eq('company_id', companyId);
      } else {
        // If no company is selected, get monitors without a company
        const isSuperadmin = await this.companyService.isSuperadmin$.pipe(
          map(isSuperadmin => isSuperadmin)
        ).toPromise();

        if (isSuperadmin) {
          // Superadmins can see all monitors
        } else {
          // Regular users can only see their own monitors when no company is selected
          const userId = await this.authService.user$.pipe(
            map(user => user?.id)
          ).toPromise();

          query = query.eq('user_id', userId);
        }
      }

      const { data, error } = await query;

      if (error) throw error;

      this.monitorsSubject.next(data || []);
    } catch (error) {
      console.error('Error loading monitors:', error);
      this.showErrorMessage('Error loading monitors', error.message || 'An unexpected error occurred');
      this.monitorsSubject.next([]);
    } finally {
      this.loadingSubject.next(false);
    }
  }

  getMonitor(id: string): Observable<any> {
    return from(supabase
      .from('monitors')
      .select('*')
      .eq('id', id)
      .single()
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Error getting monitor:', error);
        this.showErrorMessage('Error getting monitor', error.message || 'An unexpected error occurred');
        return of(null);
      })
    );
  }

  createMonitor(monitorData: any): Observable<any> {
    return this.authService.user$.pipe(
      switchMap(user => {
        if (!user) {
          throw new Error('User not authenticated');
        }

        const currentCompany = this.companyService.currentCompany$.getValue();
        
        const monitor = {
          ...monitorData,
          user_id: user.id,
          company_id: currentCompany?.id || null,
          active: true
        };

        return from(supabase
          .from('monitors')
          .insert(monitor)
          .select()
        );
      }),
      tap(({ data, error }) => {
        if (error) throw error;
        
        // Reload monitors
        const currentCompany = this.companyService.currentCompany$.getValue();
        this.loadMonitors(currentCompany?.id);
        
        this.showSuccessMessage('Monitor created', 'Monitor has been created successfully');
      }),
      map(({ data }) => data?.[0] || null),
      catchError(error => {
        console.error('Error creating monitor:', error);
        this.showErrorMessage('Error creating monitor', error.message || 'An unexpected error occurred');
        return of(null);
      })
    );
  }

  updateMonitor(id: string, monitorData: any): Observable<any> {
    return from(supabase
      .from('monitors')
      .update(monitorData)
      .eq('id', id)
      .select()
    ).pipe(
      tap(({ data, error }) => {
        if (error) throw error;
        
        // Reload monitors
        const currentCompany = this.companyService.currentCompany$.getValue();
        this.loadMonitors(currentCompany?.id);
        
        this.showSuccessMessage('Monitor updated', 'Monitor has been updated successfully');
      }),
      map(({ data }) => data?.[0] || null),
      catchError(error => {
        console.error('Error updating monitor:', error);
        this.showErrorMessage('Error updating monitor', error.message || 'An unexpected error occurred');
        return of(null);
      })
    );
  }

  deleteMonitor(id: string): Observable<boolean> {
    return this.authService.user$.pipe(
      switchMap(user => {
        if (!user) {
          throw new Error('User not authenticated');
        }

        return from(supabase
          .from('monitors')
          .update({
            deleted: true,
            deleted_at: new Date().toISOString(),
            deleted_by: user.id
          })
          .eq('id', id)
        );
      }),
      tap(({ error }) => {
        if (error) throw error;
        
        // Reload monitors
        const currentCompany = this.companyService.currentCompany$.getValue();
        this.loadMonitors(currentCompany?.id);
        
        this.showSuccessMessage('Monitor deleted', 'Monitor has been deleted successfully');
      }),
      map(() => true),
      catchError(error => {
        console.error('Error deleting monitor:', error);
        this.showErrorMessage('Error deleting monitor', error.message || 'An unexpected error occurred');
        return of(false);
      })
    );
  }

  toggleMonitorStatus(id: string, active: boolean): Observable<any> {
    return from(supabase
      .from('monitors')
      .update({ active })
      .eq('id', id)
      .select()
    ).pipe(
      tap(({ data, error }) => {
        if (error) throw error;
        
        // Reload monitors
        const currentCompany = this.companyService.currentCompany$.getValue();
        this.loadMonitors(currentCompany?.id);
        
        this.showSuccessMessage(
          active ? 'Monitor activated' : 'Monitor paused', 
          `Monitor has been ${active ? 'activated' : 'paused'} successfully`
        );
      }),
      map(({ data }) => data?.[0] || null),
      catchError(error => {
        console.error('Error toggling monitor status:', error);
        this.showErrorMessage('Error updating monitor', error.message || 'An unexpected error occurred');
        return of(null);
      })
    );
  }

  getMonitorHistory(monitorId: string, limit: number = 100): Observable<any[]> {
    return from(supabase
      .from('monitor_history')
      .select('*')
      .eq('monitor_id', monitorId)
      .order('timestamp', { ascending: false })
      .limit(limit)
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data || [];
      }),
      catchError(error => {
        console.error('Error getting monitor history:', error);
        return of([]);
      })
    );
  }

  getLatestMonitorHistory(monitorIds: string[]): Observable<Record<string, any>> {
    if (!monitorIds.length) {
      return of({});
    }

    return from(supabase
      .rpc('get_latest_monitor_history', {
        p_monitor_ids: monitorIds
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        
        // Convert array to record with monitor_id as key
        const historyMap: Record<string, any> = {};
        (data || []).forEach(item => {
          historyMap[item.monitor_id] = item;
        });
        
        return historyMap;
      }),
      catchError(error => {
        console.error('Error getting latest monitor history:', error);
        return of({});
      })
    );
  }

  getStatusCounts(): Observable<any> {
    return this.monitors$.pipe(
      switchMap(monitors => {
        if (!monitors.length) {
          return of({
            total: 0,
            up: 0,
            down: 0,
            degraded: 0,
            paused: 0
          });
        }

        const monitorIds = monitors.map(m => m.id);
        
        return this.getLatestMonitorHistory(monitorIds).pipe(
          map(historyMap => {
            let up = 0;
            let down = 0;
            let degraded = 0;
            let paused = 0;
            
            monitors.forEach(monitor => {
              if (!monitor.active) {
                paused++;
                return;
              }
              
              const history = historyMap[monitor.id];
              if (!history) {
                // No history yet, count as up
                up++;
                return;
              }
              
              // Check if status is a string (new format) or boolean (old format)
              if (typeof history.status === 'string') {
                if (history.status === 'up') up++;
                else if (history.status === 'down') down++;
                else if (history.status === 'degraded') degraded++;
              } else {
                // Handle legacy boolean status
                if (history.status) up++;
                else down++;
              }
            });
            
            return {
              total: monitors.length,
              up,
              down,
              degraded,
              paused
            };
          })
        );
      })
    );
  }

  checkMonitorNow(monitorId: string): Observable<any> {
    return from(supabase
      .rpc('trigger_monitor_check', {
        p_monitor_id: monitorId
      })
    ).pipe(
      tap(({ error }) => {
        if (error) throw error;
        this.showSuccessMessage('Check initiated', 'Monitor check has been initiated');
      }),
      map(() => true),
      catchError(error => {
        console.error('Error triggering monitor check:', error);
        this.showErrorMessage('Error checking monitor', error.message || 'An unexpected error occurred');
        return of(false);
      })
    );
  }

  getDegradedSettings(monitorId: string): Observable<any> {
    return from(supabase
      .from('monitor_degraded_settings')
      .select('*')
      .eq('monitor_id', monitorId)
      .maybeSingle()
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Error getting degraded settings:', error);
        return of(null);
      })
    );
  }

  saveDegradedSettings(monitorId: string, settings: any): Observable<any> {
    return this.getDegradedSettings(monitorId).pipe(
      switchMap(existingSettings => {
        if (existingSettings) {
          // Update existing settings
          return from(supabase
            .from('monitor_degraded_settings')
            .update({
              response_time_threshold: settings.response_time_threshold,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingSettings.id)
            .select()
          );
        } else {
          // Create new settings
          return from(supabase
            .from('monitor_degraded_settings')
            .insert({
              monitor_id: monitorId,
              response_time_threshold: settings.response_time_threshold,
              created_at: new Date().toISOString()
            })
            .select()
          );
        }
      }),
      tap(({ data, error }) => {
        if (error) throw error;
        this.showSuccessMessage('Settings saved', 'Degraded settings have been saved successfully');
      }),
      map(({ data }) => data?.[0] || null),
      catchError(error => {
        console.error('Error saving degraded settings:', error);
        this.showErrorMessage('Error saving settings', error.message || 'An unexpected error occurred');
        return of(null);
      })
    );
  }

  private showSuccessMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showErrorMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
