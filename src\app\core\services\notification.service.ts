import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, from, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { supabase } from '../../shared/supabase/supabase.client';
import { CompanyService } from './company.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notificationsSubject = new BehaviorSubject<any[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);
  private loadingSubject = new BehaviorSubject<boolean>(true);

  notifications$ = this.notificationsSubject.asObservable();
  unreadCount$ = this.unreadCountSubject.asObservable();
  loading$ = this.loadingSubject.asObservable();

  constructor(
    private companyService: CompanyService,
    private snackBar: MatSnackBar
  ) {
    // Load notifications when company changes
    this.companyService.currentCompany$.subscribe(company => {
      this.loadNotifications(company?.id);
    });

    // Set up real-time subscription for notifications
    this.setupRealtimeSubscription();
  }

  private setupRealtimeSubscription() {
    const subscription = supabase
      .channel('notifications-channel')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications'
      }, (payload) => {
        // When a new notification is inserted, reload notifications
        const currentCompany = this.companyService.currentCompany$.getValue();
        this.loadNotifications(currentCompany?.id);
      })
      .subscribe();

    // Clean up subscription when service is destroyed
    window.addEventListener('beforeunload', () => {
      supabase.removeChannel(subscription);
    });
  }

  private async loadNotifications(companyId?: string) {
    try {
      this.loadingSubject.next(true);

      let query = supabase
        .from('notifications')
        .select(`
          *,
          monitor:monitors(id, name, type, target)
        `)
        .order('created_at', { ascending: false })
        .limit(50);

      if (companyId) {
        query = query.eq('company_id', companyId);
      }

      const { data, error } = await query;

      if (error) throw error;

      this.notificationsSubject.next(data || []);
      
      // Count unread notifications
      const unreadCount = (data || []).filter(n => !n.read).length;
      this.unreadCountSubject.next(unreadCount);
    } catch (error) {
      console.error('Error loading notifications:', error);
      this.showErrorMessage('Error loading notifications', error.message || 'An unexpected error occurred');
      this.notificationsSubject.next([]);
      this.unreadCountSubject.next(0);
    } finally {
      this.loadingSubject.next(false);
    }
  }

  markAsRead(notificationId: string): Observable<boolean> {
    return from(supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId)
    ).pipe(
      tap(({ error }) => {
        if (error) throw error;
        
        // Update local state
        const notifications = this.notificationsSubject.getValue();
        const updatedNotifications = notifications.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        );
        this.notificationsSubject.next(updatedNotifications);
        
        // Update unread count
        const unreadCount = updatedNotifications.filter(n => !n.read).length;
        this.unreadCountSubject.next(unreadCount);
      }),
      map(() => true),
      catchError(error => {
        console.error('Error marking notification as read:', error);
        return of(false);
      })
    );
  }

  markAllAsRead(): Observable<boolean> {
    const currentCompany = this.companyService.currentCompany$.getValue();
    
    let query = supabase
      .from('notifications')
      .update({ read: true });
      
    if (currentCompany) {
      query = query.eq('company_id', currentCompany.id);
    }
    
    return from(query).pipe(
      tap(({ error }) => {
        if (error) throw error;
        
        // Update local state
        const notifications = this.notificationsSubject.getValue();
        const updatedNotifications = notifications.map(n => ({ ...n, read: true }));
        this.notificationsSubject.next(updatedNotifications);
        
        // Update unread count
        this.unreadCountSubject.next(0);
        
        this.showSuccessMessage('Notifications', 'All notifications marked as read');
      }),
      map(() => true),
      catchError(error => {
        console.error('Error marking all notifications as read:', error);
        this.showErrorMessage('Error', 'Failed to mark notifications as read');
        return of(false);
      })
    );
  }

  private showSuccessMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showErrorMessage(title: string, message: string): void {
    this.snackBar.open(`${title}: ${message}`, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
