import React from 'react';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { getBackendConfig, getBackendDescription } from '@/utils/backend-config';

/**
 * BackendStatus Component
 * 
 * Displays the current backend configuration status in the UI.
 * Shows whether the app is connected to local or external backend.
 */
export default function BackendStatus() {
  const config = getBackendConfig();
  const description = getBackendDescription();
  
  const badgeVariant = config.isValid 
    ? (config.mode === 'external' ? 'default' : 'secondary')
    : 'destructive';
  
  const badgeText = config.mode === 'external' ? 'External' : 'Local';
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant={badgeVariant} className="text-xs">
            {config.mode === 'external' ? '🌐' : '🏠'} {badgeText}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm">
            <div className="font-medium">{description}</div>
            {!config.isValid && (
              <div className="text-red-400 mt-1">
                <div>Configuration errors:</div>
                <ul className="list-disc list-inside">
                  {config.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
