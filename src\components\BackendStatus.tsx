import React from 'react';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { getBackendServiceConfig, getBackendServiceDescription } from '@/utils/backend-config';

/**
 * BackendStatus Component
 *
 * Displays the current VUM Backend service configuration status in the UI.
 * Shows whether the VUM Backend monitoring service is running locally or on an external server.
 */
export default function BackendStatus() {
  const config = getBackendServiceConfig();
  const description = getBackendServiceDescription();

  const badgeVariant = config.isValid
    ? (config.mode === 'external' ? 'default' : 'secondary')
    : 'destructive';

  const badgeText = config.mode === 'external' ? 'External' : 'Local';

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant={badgeVariant} className="text-xs">
            {config.mode === 'external' ? '🌐' : '🏠'} {badgeText}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm">
            <div className="font-medium">{description}</div>
            <div className="text-gray-400 mt-1">
              Supabase: ☁️ {config.supabaseUrl}
            </div>
            {!config.isValid && (
              <div className="text-red-400 mt-1">
                <div>Configuration errors:</div>
                <ul className="list-disc list-inside">
                  {config.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
