import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { getBackendServiceConfig } from '@/utils/backend-config';

/**
 * BackendStatus Component
 *
 * Displays the current VUM Backend service configuration in the UI.
 * Shows whether the VUM Backend monitoring service is configured to run locally or externally.
 */
export default function BackendStatus() {
  const config = getBackendServiceConfig();

  // Determine badge variant based on configuration
  const getBadgeVariant = () => {
    if (!config.isValid) return 'destructive';
    return config.mode === 'external' ? 'default' : 'secondary';
  };

  // Determine badge text and icon
  const getBadgeContent = () => {
    const modeIcon = config.mode === 'external' ? '🌐' : '🏠';
    const modeText = config.mode === 'external' ? 'External' : 'Local';
    return `${modeIcon} ${modeText}`;
  };



  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Badge
          variant={getBadgeVariant()}
          className="text-xs cursor-help"
        >
          {getBadgeContent()}
        </Badge>
      </TooltipTrigger>
      <TooltipContent side="bottom">
        <div className="text-sm max-w-xs">
          <div className="font-medium">
            VUM Backend Service ({config.mode})
          </div>

          {config.backendUrl && (
            <div className="text-gray-300 mt-1">
              URL: {config.backendUrl}
            </div>
          )}

          <div className="text-gray-300 mt-1">
            Supabase: ☁️ {config.supabaseUrl}
          </div>

          <div className="mt-2 pt-2 border-t border-gray-600 text-xs">
            <div className="text-gray-400">
              The VUM Backend service runs independently and connects to Supabase for monitoring.
              {config.mode === 'external'
                ? ' It should be running on your external server.'
                : ' It should be running locally with npm start.'}
            </div>
          </div>

          {!config.isValid && (
            <div className="text-red-400 mt-2 pt-2 border-t border-gray-600">
              <div className="text-xs">Configuration errors:</div>
              <ul className="list-disc list-inside text-xs">
                {config.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}


        </div>
      </TooltipContent>
    </Tooltip>
  );
}
