import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { getBackendServiceConfig } from '@/utils/backend-config';
import { useBackendHealth } from '@/hooks/use-backend-health';

/**
 * BackendStatus Component
 *
 * Displays the current VUM Backend service health status in the UI.
 * Shows whether the VUM Backend monitoring service is running and reachable.
 */
export default function BackendStatus() {
  const config = getBackendServiceConfig();
  const { healthInfo, isHealthy, isChecking, isUnhealthy, checkHealth } = useBackendHealth();

  // Determine badge variant based on health status
  const getBadgeVariant = () => {
    if (!config.isValid) return 'destructive';
    if (isChecking) return 'outline';
    if (isHealthy) return config.mode === 'external' ? 'default' : 'secondary';
    if (isUnhealthy) return 'destructive';
    return 'outline'; // unknown status
  };

  // Determine badge text and icon
  const getBadgeContent = () => {
    const modeIcon = config.mode === 'external' ? '🌐' : '🏠';
    const modeText = config.mode === 'external' ? 'External' : 'Local';

    if (isChecking) return `⏳ ${modeText}`;
    if (isHealthy) return `✅ ${modeText}`;
    if (isUnhealthy) return `❌ ${modeText}`;
    return `${modeIcon} ${modeText}`;
  };

  const formatLastChecked = (date: Date | null) => {
    if (!date) return 'Never';
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);

    if (diffSeconds < 60) return `${diffSeconds}s ago`;
    const diffMinutes = Math.floor(diffSeconds / 60);
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    const diffHours = Math.floor(diffMinutes / 60);
    return `${diffHours}h ago`;
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Badge
          variant={getBadgeVariant()}
          className="text-xs cursor-help"
          onClick={() => checkHealth()}
        >
          {getBadgeContent()}
        </Badge>
      </TooltipTrigger>
      <TooltipContent side="bottom">
        <div className="text-sm max-w-xs">
          <div className="font-medium">
            VUM Backend Service ({config.mode})
          </div>

          {config.backendUrl && (
            <div className="text-gray-300 mt-1">
              URL: {config.backendUrl}
            </div>
          )}

          <div className="text-gray-300 mt-1">
            Supabase: ☁️ {config.supabaseUrl}
          </div>

          <div className="mt-2 pt-2 border-t border-gray-600">
            <div className="text-xs">
              <div>Status: {healthInfo.status}</div>
              <div>Last checked: {formatLastChecked(healthInfo.lastChecked)}</div>
              {healthInfo.responseTime && (
                <div>Response time: {healthInfo.responseTime}ms</div>
              )}
              {healthInfo.error && (
                <div className="text-red-400 mt-1">
                  Error: {healthInfo.error}
                </div>
              )}
            </div>
          </div>

          {!config.isValid && (
            <div className="text-red-400 mt-2 pt-2 border-t border-gray-600">
              <div className="text-xs">Configuration errors:</div>
              <ul className="list-disc list-inside text-xs">
                {config.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="text-xs text-gray-400 mt-2">
            Click to refresh status
          </div>
        </div>
      </TooltipContent>
    </Tooltip>
  );
}
