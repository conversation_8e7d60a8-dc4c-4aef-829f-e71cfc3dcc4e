import React, { useEffect } from 'react';
import { useCompany } from '@/contexts/CompanyContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Building, ChevronDown, Plus } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useCompanyRoles } from '@/hooks/use-company-roles';

interface CompanySelectorProps {
  className?: string;
}

const CompanySelector: React.FC<CompanySelectorProps> = ({ className }) => {
  const { companies, currentCompany, setCurrentCompany, isAdmin } = useCompany();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin = false } = useGlobalSuperadminQuery();
  const navigate = useNavigate();
  const location = useLocation();

  // Clear the company_changed flag when the component mounts
  useEffect(() => {
    localStorage.removeItem('company_changed');
  }, []);

  if (!companies.length) {
    // Only show create company button for superadmins
    if (isSuperadmin) {
      return (
        <Button
          variant="outline"
          size="sm"
          className={className}
          onClick={() => navigate('/create-company')}
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Company
        </Button>
      );
    } else {
      // For non-superadmins, show a disabled button
      return (
        <Button
          variant="outline"
          size="sm"
          className={className}
          disabled
        >
          <Building className="h-4 w-4 mr-2" />
          No Companies
        </Button>
      );
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          <Building className="h-4 w-4 mr-2" />
          {currentCompany?.name || 'Select Company'}
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-56">
        <DropdownMenuLabel>Your Companies</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {/* Add "All" option for superadmins */}
        {isSuperadmin && (
          <DropdownMenuItem
            onClick={() => {
              // Check if we're on a monitor detail or edit page
              const currentPath = window.location.pathname;
              const isMonitorPage = currentPath.includes('/monitor/') || currentPath.includes('/edit-monitor/');

              // Set the company
              setCurrentCompany({ id: 'all', name: 'All Companies', role: 'superadmin', isAllCompanies: true });

              // If we're on a monitor page, navigate to dashboard
              if (isMonitorPage) {
                navigate('/dashboard');
              }
            }}
            className={currentCompany?.isAllCompanies ? 'bg-slate-100 dark:bg-slate-800' : ''}
          >
            <span className="font-semibold">All Companies</span>
          </DropdownMenuItem>
        )}
        {companies.map((company) => (
          <DropdownMenuItem
            key={company.id}
            onClick={() => {
              // Check if we're on a monitor detail or edit page
              const currentPath = window.location.pathname;
              const isMonitorPage = currentPath.includes('/monitor/') || currentPath.includes('/edit-monitor/');

              // Set the company
              setCurrentCompany(company);

              // If we're on a monitor page, navigate to dashboard
              if (isMonitorPage) {
                navigate('/dashboard');
              }
            }}
            className={currentCompany?.id === company.id ? 'bg-slate-100 dark:bg-slate-800' : ''}
          >
            {company.name}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        {isSuperadmin && (
          <DropdownMenuItem onClick={() => navigate('/create-company')}>
            <Plus className="h-4 w-4 mr-2" />
            Create New Company
          </DropdownMenuItem>
        )}
        {isAdmin && currentCompany && !currentCompany.isAllCompanies && (
          <DropdownMenuItem onClick={() => navigate(`/company/${currentCompany.id}/settings`)}>
            Manage Company
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CompanySelector;
