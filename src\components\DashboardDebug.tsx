import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useCompany } from '@/contexts/CompanyContext';
import { useMonitors } from '@/hooks/use-monitors';
import { useCompanyRoles } from '@/hooks/use-company-roles';

/**
 * Debug component to help diagnose dashboard issues
 */
export default function DashboardDebug() {
  const { user } = useAuth();
  const { currentCompany, companies, isLoading: companyLoading, error: companyError } = useCompany();
  const { useGetMonitorsQuery } = useMonitors();
  const { data: monitors, isLoading: monitorsLoading, error: monitorsError } = useGetMonitorsQuery();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin = false } = useGlobalSuperadminQuery();

  return (
    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4">
      <h3 className="text-lg font-semibold mb-3">Dashboard Debug Info</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>User:</strong> {user ? `${user.email} (${user.id})` : 'Not logged in'}
        </div>
        
        <div>
          <strong>Is Superadmin:</strong> {isSuperadmin ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>Companies Loading:</strong> {companyLoading ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>Company Error:</strong> {companyError ? companyError.message : 'None'}
        </div>
        
        <div>
          <strong>Available Companies:</strong> {companies.length} 
          {companies.length > 0 && (
            <ul className="ml-4 mt-1">
              {companies.map(company => (
                <li key={company.id}>
                  {company.name} ({company.id})
                </li>
              ))}
            </ul>
          )}
        </div>
        
        <div>
          <strong>Current Company:</strong> {currentCompany ? `${currentCompany.name} (${currentCompany.id})` : 'None selected'}
        </div>
        
        <div>
          <strong>Monitors Loading:</strong> {monitorsLoading ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>Monitors Error:</strong> {monitorsError ? String(monitorsError) : 'None'}
        </div>
        
        <div>
          <strong>Monitors Found:</strong> {monitors ? monitors.length : 'None'}
          {monitors && monitors.length > 0 && (
            <ul className="ml-4 mt-1">
              {monitors.slice(0, 5).map(monitor => (
                <li key={monitor.id}>
                  {monitor.name} - {monitor.status} ({monitor.id})
                </li>
              ))}
              {monitors.length > 5 && <li>... and {monitors.length - 5} more</li>}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
}
