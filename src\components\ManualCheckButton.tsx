import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2, RefreshCw } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { ToastAction } from '@/components/ui/toast';
import { supabase } from '@/integrations/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import { useMonitorStatusUpdate } from '@/hooks/use-monitor-status-update';

interface ManualCheckButtonProps {
  monitorId: string; // Required - only checks this specific monitor
  className?: string;
}

const ManualCheckButton = ({ monitorId, className }: ManualCheckButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const { updateMonitorStatus } = useMonitorStatusUpdate();

  const triggerCheck = async () => {
    console.log('Check Now button clicked, monitorId:', monitorId);
    setIsLoading(true);

    try {
      console.log('Calling monitor-checker edge function...');
      // Call the monitor-checker edge function with the specific monitorId and manual check flag
      const { data, error } = await supabase.functions.invoke('monitor-checker', {
        body: {
          monitorId,
          isManualCheck: true
        },
      });

      console.log('Edge function response:', { data, error });

      if (error) {
        console.error('Error from edge function:', error);
        throw error;
      }

      // Invalidate and refetch all monitor-related queries to ensure UI is up to date
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['monitors'] }),
        queryClient.invalidateQueries({ queryKey: ['monitor', monitorId] }),
        queryClient.invalidateQueries({ queryKey: ['monitor-history-4h', monitorId] }),
        queryClient.invalidateQueries({ queryKey: ['monitorHistory', 'latest'] }),
        queryClient.refetchQueries({ queryKey: ['monitors'] }),
        queryClient.refetchQueries({ queryKey: ['monitor', monitorId] }),
        queryClient.refetchQueries({ queryKey: ['monitor-history-4h', monitorId] }),
        queryClient.refetchQueries({ queryKey: ['monitorHistory', 'latest'] })
      ]);

      // If we have results for the monitor, show detailed status
      console.log('Processing results:', data);
      if (data.results && data.results.length > 0) {
        console.log(`Found ${data.results.length} results`);
        const result = data.results[0];
        const status = result.status ? 'UP' : 'DOWN';
        // Format the timestamp
        const timestamp = new Date().toLocaleTimeString();

        // Get the monitor type
        const { data: monitorData } = await supabase
          .from('monitors')
          .select('type')
          .eq('id', monitorId)
          .single();

        // Update the monitor status in our store for immediate UI update
        // Note: The edge function will update the database with the correct status (including degraded)
        // We'll use a simple conversion here and let the query invalidation fetch the real status
        updateMonitorStatus(
          monitorId,
          result.status ? 'up' : 'down',
          result.response_time,
          monitorData?.type,
          data.portResults // Include port results for port monitors
        );

        toast({
          title: `Monitor is ${status}`,
          description: (
            <div className="space-y-1">
              <p><strong>Time:</strong> {timestamp}</p>
              <p><strong>Response time:</strong> {result.response_time}ms</p>
              {result.error_message && (
                <p className="text-red-500"><strong>Error:</strong> {result.error_message}</p>
              )}

              {/* Show port-specific information for port monitors */}
              {monitorData?.type === 'port' && data.portResults && (
                <div className="mt-2 space-y-1">
                  <p><strong>Port Details:</strong></p>
                  <div className="max-h-32 overflow-y-auto">
                    {data.portResults.map((port) => (
                      <div key={port.port} className="flex items-center space-x-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${port.status ? 'bg-green-500' : 'bg-red-500'}`}></span>
                        <span>Port {port.port}: {port.status ? 'Open' : 'Closed'}</span>
                        {port.error && <span className="text-xs text-red-500">({port.error})</span>}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ),
          variant: result.status ? 'default' : 'destructive',
          action: (
            <ToastAction altText="View details" onClick={() => {
              // Navigate to monitor details page
              window.location.href = `/monitor/${monitorId}`;
            }}>
              View Details
            </ToastAction>
          ),
        });
      } else {
        console.log('No results returned from check');
        toast({
          title: 'Monitor Checked',
          description: 'The check was completed but no results were returned.',
          variant: 'default'
        });
      }
    } catch (err) {
      console.error('Error triggering monitor check:', err);

      // Show a more detailed error message
      let errorMessage = 'Failed to trigger monitor check. Check console for details.';
      if (err instanceof Error) {
        console.error('Error details:', {
          name: err.name,
          message: err.message,
          stack: err.stack,
          cause: (err as any).cause
        });
        errorMessage = `Error: ${err.message}`;
      } else {
        console.error('Non-Error object thrown:', err);
      }

      toast({
        title: 'Error Testing Monitor',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={triggerCheck}
      disabled={isLoading}
      className={className}
    >
      {isLoading ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Checking...
        </>
      ) : (
        <>
          <RefreshCw className="h-4 w-4 mr-2" />
          Check Now
        </>
      )}
    </Button>
  );
};

export default ManualCheckButton;
