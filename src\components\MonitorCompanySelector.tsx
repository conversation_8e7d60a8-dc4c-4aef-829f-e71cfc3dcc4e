import React, { useState, useEffect } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { useCompany } from '@/contexts/CompanyContext';
import { Company } from '@/types/company';

interface MonitorCompanySelectorProps {
  selectedCompanyIds: string[];
  onChange: (companyIds: string[]) => void;
  className?: string;
}

export function MonitorCompanySelector({
  selectedCompanyIds,
  onChange,
  className,
}: MonitorCompanySelectorProps) {
  const { companies } = useCompany();
  const [open, setOpen] = useState(false);

  // Toggle a company selection
  const toggleCompany = (companyId: string) => {
    if (selectedCompanyIds.includes(companyId)) {
      onChange(selectedCompanyIds.filter(id => id !== companyId));
    } else {
      onChange([...selectedCompanyIds, companyId]);
    }
  };

  // Get selected company names for display
  const getSelectedCompanyNames = () => {
    return companies
      .filter(company => selectedCompanyIds.includes(company.id))
      .map(company => company.name);
  };

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedCompanyIds.length > 0
              ? `${selectedCompanyIds.length} ${selectedCompanyIds.length === 1 ? 'company' : 'companies'} selected`
              : "Select companies..."}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search companies..." />
            <CommandList>
              <CommandEmpty>No companies found.</CommandEmpty>
              <CommandGroup>
                {companies.map((company) => (
                  <CommandItem
                    key={company.id}
                    value={company.id}
                    onSelect={() => toggleCompany(company.id)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedCompanyIds.includes(company.id) ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {company.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Display selected companies as badges */}
      <div className="flex flex-wrap gap-2 mt-2">
        {getSelectedCompanyNames().map((name) => (
          <Badge key={name} variant="secondary">
            {name}
          </Badge>
        ))}
      </div>
    </div>
  );
}

export default MonitorCompanySelector;
