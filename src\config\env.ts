/**
 * Environment configuration for the application
 * This file centralizes access to environment variables
 */

// Supabase configuration
// NOTE: These are now accessed directly in the Supabase client
// to avoid initialization order issues
export const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "";
export const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "";

// Application configuration
export const APP_NAME = "Vurbis Uptime Monitor";
export const APP_VERSION = import.meta.env.VITE_APP_VERSION || "1.0.0";
export const NODE_ENV = import.meta.env.MODE || "development";
export const IS_PRODUCTION = NODE_ENV === "production";
export const IS_DEVELOPMENT = NODE_ENV === "development";
export const IS_TEST = NODE_ENV === "test";

// Feature flags
export const ENABLE_ANALYTICS = import.meta.env.VITE_ENABLE_ANALYTICS === "true" || false;
