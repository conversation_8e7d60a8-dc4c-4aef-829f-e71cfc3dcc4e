import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './AuthContext';
import { toast } from '@/components/ui/use-toast';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import { logAction, getClientIp, getUserAgent } from '@/utils/auditLogger';
import { useQueryClient } from '@tanstack/react-query';
import {
  Company,
  CompanyMember,
  CompanyWithMembers,
  CompanyRole,
  CreateCompanyData,
  UpdateCompanyData,
  AddCompanyMemberData,
  UpdateCompanyMemberData
} from '@/types/company';

interface CompanyContextType {
  companies: Company[];
  currentCompany: Company | null;
  companyMembers: CompanyMember[];
  isAdmin: boolean;
  isLoading: boolean;
  error: Error | null;
  fetchCompanies: (force?: boolean) => Promise<void>;
  fetchCompanyMembers: (companyId: string) => Promise<CompanyMember[]>;
  createCompany: (data: CreateCompanyData) => Promise<Company | null>;
  updateCompany: (id: string, data: UpdateCompanyData) => Promise<Company | null>;
  deleteCompany: (id: string) => Promise<boolean>;
  addCompanyMember: (data: AddCompanyMemberData) => Promise<CompanyMember | null>;
  updateCompanyMember: (id: string, data: UpdateCompanyMemberData) => Promise<CompanyMember | null>;
  removeCompanyMember: (id: string) => Promise<boolean>;
  setCurrentCompany: (company: Company | null) => void;
  getUserRole: (companyId: string) => CompanyRole | null;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export const CompanyProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const companyRoles = useCompanyRoles();
  const { useGlobalSuperadminQuery } = companyRoles;
  const { data: isGlobalSuperadmin = false } = useGlobalSuperadminQuery();
  const queryClient = useQueryClient();

  const [companies, setCompanies] = useState<Company[]>([]);
  const [currentCompany, setCurrentCompanyState] = useState<Company | null>(null);

  // Wrapper for setCurrentCompany that also updates localStorage, invalidates queries, and navigates if needed
  const setCurrentCompany = (company: Company | null) => {
    // Only proceed if the company is actually changing
    if (currentCompany?.id !== company?.id) {
      console.log(`Changing company from ${currentCompany?.name || 'null'} to ${company?.name || 'null'}`);

      // Update state
      setCurrentCompanyState(company);

      // Store the selected company ID in localStorage for persistence
      if (company) {
        localStorage.setItem('lastSelectedCompanyId', company.id);
      } else {
        localStorage.removeItem('lastSelectedCompanyId');
      }

      // Invalidate all monitor-related queries to force a refresh
      console.log('Invalidating monitor queries due to company change');
      queryClient.invalidateQueries({ queryKey: ['monitors'] });

      // Also invalidate any other queries that might depend on the company
      queryClient.invalidateQueries({ queryKey: ['monitor-history'] });
      queryClient.invalidateQueries({ queryKey: ['notifications'] });

      // Check if we need to navigate to the dashboard
      // We'll store a flag in localStorage to indicate that we need to navigate
      localStorage.setItem('company_changed', 'true');
    }
  };
  const [companyMembers, setCompanyMembers] = useState<CompanyMember[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Track if we're currently fetching companies
  const [isFetchingCompanies, setIsFetchingCompanies] = useState<boolean>(false);

  // Track if we've already fetched companies for this user
  const [hasFetchedCompanies, setHasFetchedCompanies] = useState<boolean>(false);

  // Also use a ref to track this across renders
  const hasFetchedCompaniesRef = useRef<boolean>(false);

  // Set the company as current if none is selected
  useEffect(() => {
    if (companies.length > 0 && !currentCompany) {
      // Check if there's a last selected company ID in localStorage
      const lastSelectedCompanyId = localStorage.getItem('lastSelectedCompanyId');

      if (lastSelectedCompanyId) {
        // Find the company with the matching ID
        const lastCompany = companies.find(company => company.id === lastSelectedCompanyId);

        if (lastCompany) {
          console.log(`Restoring last selected company: ${lastCompany.name} (${lastCompany.id})`);
          setCurrentCompany(lastCompany);
          return;
        }
      }

      // If no last selected company or it wasn't found, use the first company
      setCurrentCompany(companies[0]);
    }
  }, [companies, currentCompany]);

  // Check if the current user is an admin of the current company
  // If there are no company members yet (tables not created), default to true to allow initial setup
  // Also, if "All Companies" is selected and the user is a superadmin, they are an admin
  const isAdmin = companyMembers.length === 0 ? true :
    (currentCompany?.isAllCompanies && isGlobalSuperadmin) ? true :
    companyMembers.some(
      member => member.user_id === user?.id &&
      (member.role_type === 'admin' || member.role_type === 'superadmin')
    );

  // Get the user's role in a specific company
  const getUserRole = (companyId: string): CompanyRole | null => {
    // If there are no company members yet (tables not created), default to admin to allow initial setup
    if (companyMembers.length === 0) return 'admin';

    // Check if the user is a global superadmin (cached value from props)
    if (isGlobalSuperadmin) {
      return 'admin'; // Global superadmins have admin access to all companies
    }

    const membership = companyMembers.find(
      member => member.company_id === companyId && member.user_id === user?.id
    );

    if (!membership) return null;

    // Map role_type to CompanyRole for UI display
    if (membership.role_type === 'admin') return 'admin';
    if (membership.role_type === 'superadmin') return 'admin'; // Treat superadmin as admin in company context
    return 'member'; // Default for 'user' role_type
  };

  // Fetch all companies the user is a member of - only called when page loads or manually refreshed
  const fetchCompanies = useCallback(async (force: boolean = false): Promise<void> => {
    // Skip if no user or already fetching
    if (!user) {
      console.log('No user, skipping fetchCompanies');
      return;
    }

    if (isFetchingCompanies) {
      console.log('Already fetching companies, skipping duplicate call');
      return;
    }

    // Skip if already fetched (unless forced)
    if (!force && hasFetchedCompaniesRef.current) {
      console.log('Companies already fetched and force=false, skipping');
      return;
    }

    // Prevent multiple simultaneous fetches
    setIsFetchingCompanies(true);
    setIsLoading(true);
    setError(null);

    try {
      // Use direct query to get companies for the current user
      console.log('Fetching companies for user:', user.id);

      // First get the company IDs for this user
      const { data: memberData, error: memberError } = await supabase
        .from('company_members')
        .select('company_id')
        .eq('user_id', user.id);

      if (memberError) {
        console.error('Error fetching company memberships:', memberError);
        setCompanies([]);
        return;
      }

      if (!memberData || memberData.length === 0) {
        console.log('User is not a member of any companies');
        setCompanies([]);
        return;
      }

      // Extract company IDs
      const companyIds = memberData.map(item => item.company_id);
      console.log('Company IDs found:', companyIds);

      // Check if the user is a superadmin
      const isSuperadmin = await companyRoles.isGlobalSuperadmin();

      // Now fetch the companies
      let query = supabase
        .from('companies')
        .select('*')
        .in('id', companyIds);

      // For non-superadmins, try to exclude deleted companies
      if (!isSuperadmin) {
        try {
          query = query.eq('deleted', false);
        } catch (err) {
          console.warn('Could not filter by deleted status, continuing without filter');
        }
      }

      let companiesData;
      let fetchError;

      try {
        // Try with the query we built
        const { data, error } = await query;
        companiesData = data;
        fetchError = error;

        if (error) {
          // If there's an error, try again without the deleted filter
          console.error('Error with initial query, trying fallback:', error);
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('companies')
            .select('*')
            .in('id', companyIds);

          companiesData = fallbackData;
          fetchError = fallbackError;
        }
      } catch (queryError) {
        console.error('Exception during query execution:', queryError);
        // Last resort fallback
        const { data: lastResortData, error: lastResortError } = await supabase
          .from('companies')
          .select('*')
          .in('id', companyIds);

        companiesData = lastResortData;
        fetchError = lastResortError;
      }

      // Handle any remaining errors
      if (fetchError) {
        console.error('Error fetching companies:', fetchError);
        setCompanies([]);
        return;
      }

      // Process the data
      console.log('Companies fetched:', companiesData);

      if (!companiesData || companiesData.length === 0) {
        console.log('No companies found');
        setCompanies([]);
        return;
      }

      // Extract unique companies from the result
      const uniqueCompanies: Company[] = [];
      const companyIdSet = new Set();

      if (companiesData && Array.isArray(companiesData)) {
        companiesData.forEach((item: any) => {
          if (!companyIdSet.has(item.id)) {
            companyIdSet.add(item.id);
            uniqueCompanies.push({
              id: item.id,
              name: item.name,
              description: item.description,
              logo_url: item.logo_url,
              created_at: item.created_at,
              updated_at: item.updated_at
            });
          }
        });
      }

      setCompanies(uniqueCompanies);
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error in fetchCompanies:', err);
      }
      setError(err instanceof Error ? err : new Error('Failed to fetch companies'));
      // Don't show toast for this error since it's likely just that the tables don't exist yet
    } finally {
      setIsLoading(false);
      setIsFetchingCompanies(false);
      hasFetchedCompaniesRef.current = true;
      setHasFetchedCompanies(true);
    }
  }, [user, isFetchingCompanies]);

  // Fetch members of a specific company
  const fetchCompanyMembers = useCallback(async (companyId: string): Promise<CompanyMember[]> => {
    if (!user) return [];

    // Handle the "All Companies" case
    if (companyId === 'all') {
      console.log('All Companies selected, skipping company members fetch');
      setCompanyMembers([]);
      return [];
    }

    try {
      console.log('Fetching members for company:', companyId);
      // Use the RPC function to get company members
      const { data, error } = await supabase
        .rpc('get_company_members', { p_company_id: companyId });

      if (error) {
        console.error('Error fetching company members:', error);
        setCompanyMembers([]);
        return [];
      }

      console.log('Company members fetched:', data);

      if (!data || data.length === 0) {
        setCompanyMembers([]);
        return [];
      }

      // Transform the data into CompanyMember objects
      const members: CompanyMember[] = data.map((item: any) => ({
        id: item.id,
        company_id: item.company_id,
        user_id: item.user_id,
        role_type: item.role_type,
        created_at: item.created_at,
        updated_at: item.updated_at,
        email: item.email,
        full_name: item.full_name,
        avatar_url: item.avatar_url
      }));

      setCompanyMembers(members);
      return members;
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching company members:', err);
      }
      // Don't show toast for this error since it's likely just that the tables don't exist yet
      setCompanyMembers([]);
      return [];
    }
  }, [user]);

  // Fetch companies ONCE when the user first logs in
  useEffect(() => {
    if (user) {
      // Only fetch if we haven't fetched yet
      if (!hasFetchedCompaniesRef.current) {
        fetchCompanies();
      }
    } else {
      // Clear state when user is null
      setCompanies([]);
      setCurrentCompany(null);
      setCompanyMembers([]);
      hasFetchedCompaniesRef.current = false;
      setHasFetchedCompanies(false);
    }
  }, [user]); // Intentionally omit fetchCompanies to prevent infinite loop

  // Fetch company members when the current company changes
  useEffect(() => {
    if (currentCompany) {
      fetchCompanyMembers(currentCompany.id);
    } else {
      setCompanyMembers([]);
    }
  }, [currentCompany, fetchCompanyMembers]);

  // Create a new company
  const createCompany = async (data: CreateCompanyData): Promise<Company | null> => {
    if (!user) return null;

    try {
      // Check if the user is a superadmin
      const isSuperadmin = await companyRoles.isGlobalSuperadmin();

      if (!isSuperadmin) {
        toast({
          title: 'Access Denied',
          description: 'Only superadmins can create companies',
          variant: 'destructive',
        });
        return null;
      }

      // Insert the company
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .insert([data])
        .select()
        .single();

      if (companyError) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error inserting company:', companyError);
        }
        throw companyError;
      }

      // Add the current user as an admin
      const { error: memberError } = await supabase
        .from('company_members')
        .insert([{
          company_id: companyData.id,
          user_id: user.id,
          role_type: 'admin'
        }]);

      if (memberError) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error adding user as admin:', memberError);
        }
        throw memberError;
      }

      // Add the new company to the state
      const newCompany: Company = {
        id: companyData.id,
        name: companyData.name,
        description: companyData.description,
        logo_url: companyData.logo_url,
        created_at: companyData.created_at,
        updated_at: companyData.updated_at
      };

      setCompanies([...companies, newCompany]);
      setCurrentCompany(newCompany);

      // Log the action
      await logAction(
        user.id,
        'create',
        'companies',
        newCompany.id,
        null,
        newCompany,
        getClientIp(),
        getUserAgent()
      );

      toast({
        title: 'Success',
        description: 'Company created successfully',
      });

      return newCompany;
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error creating company:', err);
      }
      toast({
        title: 'Error',
        description: 'Failed to create company',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Update a company
  const updateCompany = async (id: string, data: UpdateCompanyData): Promise<Company | null> => {
    if (!user) return null;

    try {
      // Get the company data before update for audit log
      const { data: oldCompanyData, error: oldDataError } = await supabase
        .from('companies')
        .select('*')
        .eq('id', id)
        .single();

      if (oldDataError) {
        console.error('Error fetching old company data:', oldDataError);
      }

      const { data: companyData, error } = await supabase
        .from('companies')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Update the company in the state
      const updatedCompany: Company = {
        id: companyData.id,
        name: companyData.name,
        description: companyData.description,
        logo_url: companyData.logo_url,
        created_at: companyData.created_at,
        updated_at: companyData.updated_at
      };

      setCompanies(companies.map(company =>
        company.id === id ? updatedCompany : company
      ));

      if (currentCompany?.id === id) {
        setCurrentCompany(updatedCompany);
      }

      // Log the action
      await logAction(
        user.id,
        'update',
        'companies',
        id,
        oldCompanyData,
        updatedCompany,
        getClientIp(),
        getUserAgent()
      );

      toast({
        title: 'Success',
        description: 'Company updated successfully',
      });

      return updatedCompany;
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error updating company:', err);
      }
      toast({
        title: 'Error',
        description: 'Failed to update company',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Delete a company (soft delete for admins, hard delete for superadmins)
  const deleteCompany = async (id: string): Promise<boolean> => {
    if (!user) return false;

    try {
      // Get the company data before deletion for audit log
      const { data: companyData, error: getError } = await supabase
        .from('companies')
        .select('*')
        .eq('id', id)
        .single();

      if (getError) {
        console.error('Error fetching company data for deletion:', getError);
      }

      // Check if the user is a superadmin
      const isSuperadmin = await companyRoles.isGlobalSuperadmin();

      let actionType = 'delete';

      if (isSuperadmin) {
        // Hard delete for superadmins
        const { error } = await supabase
          .from('companies')
          .delete()
          .eq('id', id);

        if (error) throw error;
        actionType = 'delete';
      } else {
        try {
          // Try soft delete for regular admins
          const { data, error } = await supabase
            .rpc('soft_delete_company', {
              company_id: id,
              user_id: user.id
            });

          if (error) {
            // If the RPC fails (possibly because the function doesn't exist yet),
            // fall back to updating the deleted flag directly
            console.error('Error with soft_delete_company RPC, trying direct update:', error);

            try {
              // Try to update the deleted flag
              const { error: updateError } = await supabase
                .from('companies')
                .update({
                  deleted: true,
                  deleted_at: new Date().toISOString(),
                  deleted_by: user.id
                })
                .eq('id', id);

              if (updateError) {
                // If that fails too, just do a hard delete
                console.error('Error with direct update, falling back to hard delete:', updateError);
                const { error: hardDeleteError } = await supabase
                  .from('companies')
                  .delete()
                  .eq('id', id);

                if (hardDeleteError) throw hardDeleteError;
                actionType = 'delete';
              } else {
                actionType = 'soft_delete';
              }
            } catch (updateErr) {
              console.error('Error with fallback methods:', updateErr);
              throw updateErr;
            }
          } else {
            actionType = 'soft_delete';
          }
        } catch (softDeleteErr) {
          console.error('Error with soft delete:', softDeleteErr);
          throw softDeleteErr;
        }
      }

      // Remove the company from the state
      setCompanies(companies.filter(company => company.id !== id));

      // If the deleted company was the current one, set the first available company as current
      if (currentCompany?.id === id) {
        const remainingCompanies = companies.filter(company => company.id !== id);
        setCurrentCompany(remainingCompanies.length > 0 ? remainingCompanies[0] : null);
      }

      // Log the action
      await logAction(
        user.id,
        actionType,
        'companies',
        id,
        companyData,
        null,
        getClientIp(),
        getUserAgent()
      );

      toast({
        title: 'Success',
        description: 'Company deleted successfully',
      });

      return true;
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error deleting company:', err);
      }
      toast({
        title: 'Error',
        description: 'Failed to delete company',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Add a member to a company
  const addCompanyMember = async (data: AddCompanyMemberData): Promise<CompanyMember | null> => {
    if (!user) return null;

    try {
      // Check if the user exists
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, email, full_name, avatar_url')
        .eq('id', data.user_id)
        .single();

      // Cast userData to any to avoid TypeScript errors
      const userInfo = userData as any;

      if (userError) {
        toast({
          title: 'Error',
          description: 'User not found',
          variant: 'destructive',
        });
        return null;
      }

      // Add the member
      const { data: memberData, error: memberError } = await supabase
        .from('company_members')
        .insert([data])
        .select()
        .single();

      if (memberError) throw memberError;

      // Create the member object with user information
      const newMember: CompanyMember = {
        id: memberData.id,
        company_id: memberData.company_id,
        user_id: memberData.user_id,
        role_type: memberData.role_type,
        created_at: memberData.created_at,
        updated_at: memberData.updated_at,
        email: userInfo?.email,
        full_name: userInfo?.full_name,
        avatar_url: userInfo?.avatar_url
      };

      // Update the members in the state
      setCompanyMembers([...companyMembers, newMember]);

      toast({
        title: 'Success',
        description: 'Member added successfully',
      });

      return newMember;
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error adding company member:', err);
      }
      toast({
        title: 'Error',
        description: 'Failed to add member',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Update a company member
  const updateCompanyMember = async (id: string, data: UpdateCompanyMemberData): Promise<CompanyMember | null> => {
    if (!user) return null;

    try {
      const { data: memberData, error } = await supabase
        .from('company_members')
        .update(data)
        .eq('id', id)
        .select('*')
        .single();

      if (error) throw error;

      // Create the updated member object
      const updatedMember: CompanyMember = {
        id: memberData.id,
        company_id: memberData.company_id,
        user_id: memberData.user_id,
        role_type: memberData.role_type,
        created_at: memberData.created_at,
        updated_at: memberData.updated_at,
        // We don't have user information from the join, so leave these fields undefined
        email: undefined,
        full_name: undefined,
        avatar_url: undefined
      };

      // Update the member in the state
      setCompanyMembers(companyMembers.map(member =>
        member.id === id ? updatedMember : member
      ));

      toast({
        title: 'Success',
        description: 'Member updated successfully',
      });

      return updatedMember;
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error updating company member:', err);
      }
      toast({
        title: 'Error',
        description: 'Failed to update member',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Remove a member from a company
  const removeCompanyMember = async (id: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const { error } = await supabase
        .from('company_members')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Remove the member from the state
      setCompanyMembers(companyMembers.filter(member => member.id !== id));

      toast({
        title: 'Success',
        description: 'Member removed successfully',
      });

      return true;
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error removing company member:', err);
      }
      toast({
        title: 'Error',
        description: 'Failed to remove member',
        variant: 'destructive',
      });
      return false;
    }
  };

  const value = {
    companies,
    currentCompany,
    companyMembers,
    isAdmin,
    isLoading,
    error,
    fetchCompanies,
    fetchCompanyMembers,
    createCompany,
    updateCompany,
    deleteCompany,
    addCompanyMember,
    updateCompanyMember,
    removeCompanyMember,
    setCurrentCompany,
    getUserRole
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
};

export const useCompany = (): CompanyContextType => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};
