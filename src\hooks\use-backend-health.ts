import { useState, useEffect, useCallback } from 'react';
import { getBackendServiceConfig } from '@/utils/backend-config';

export type HealthStatus = 'checking' | 'healthy' | 'unhealthy' | 'unknown';

export interface BackendHealthInfo {
  status: HealthStatus;
  lastChecked: Date | null;
  responseTime: number | null;
  error: string | null;
  url: string;
}

/**
 * Hook for checking VUM Backend service health
 */
export function useBackendHealth() {
  const [healthInfo, setHealthInfo] = useState<BackendHealthInfo>({
    status: 'unknown',
    lastChecked: null,
    responseTime: null,
    error: null,
    url: ''
  });

  const config = getBackendServiceConfig();

  const checkHealth = useCallback(async (): Promise<BackendHealthInfo> => {
    const startTime = Date.now();
    const url = config.backendUrl;

    // If no URL configured, return unknown status
    if (!url) {
      return {
        status: 'unknown',
        lastChecked: new Date(),
        responseTime: null,
        error: 'Backend URL not configured',
        url: ''
      };
    }

    try {
      setHealthInfo(prev => ({ ...prev, status: 'checking', url }));

      // Try to ping the health endpoint
      const healthUrl = `${url}/health`;
      const response = await fetch(healthUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Set a reasonable timeout
        signal: AbortSignal.timeout(5000)
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        const result: BackendHealthInfo = {
          status: 'healthy',
          lastChecked: new Date(),
          responseTime,
          error: null,
          url
        };
        setHealthInfo(result);
        return result;
      } else {
        const result: BackendHealthInfo = {
          status: 'unhealthy',
          lastChecked: new Date(),
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`,
          url
        };
        setHealthInfo(result);
        return result;
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      const result: BackendHealthInfo = {
        status: 'unhealthy',
        lastChecked: new Date(),
        responseTime,
        error: errorMessage,
        url
      };
      setHealthInfo(result);
      return result;
    }
  }, [config.backendUrl]);

  // Auto-check health on mount and when URL changes
  useEffect(() => {
    if (config.backendUrl) {
      checkHealth();
    }
  }, [config.backendUrl, checkHealth]);

  // Periodic health checks every 30 seconds
  useEffect(() => {
    if (!config.backendUrl) return;

    const interval = setInterval(() => {
      checkHealth();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [config.backendUrl, checkHealth]);

  return {
    healthInfo,
    checkHealth,
    isHealthy: healthInfo.status === 'healthy',
    isChecking: healthInfo.status === 'checking',
    isUnhealthy: healthInfo.status === 'unhealthy',
    isUnknown: healthInfo.status === 'unknown'
  };
}
