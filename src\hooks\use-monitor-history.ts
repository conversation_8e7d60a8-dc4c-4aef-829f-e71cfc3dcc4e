import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useCompany } from '@/contexts/CompanyContext';
import { Database } from '@/integrations/supabase/types';

type MonitorHistory = Database['public']['Tables']['monitor_history']['Row'];

export function useMonitorHistory() {
  const { user } = useAuth();
  const { currentCompany } = useCompany();

  // Get the company's history retention period
  const getCompanyHistoryRetention = async (companyId?: string): Promise<number> => {
    if (!user) return 7; // Default to 7 days

    try {
      if (companyId) {
        // Try to get company-specific retention period
        const { data: companyData, error: companyError } = await supabase
          .from('companies')
          .select('history_retention_days')
          .eq('id', companyId)
          .single();

        if (!companyError && companyData && companyData.history_retention_days) {
          return companyData.history_retention_days;
        }
      }

      // Fall back to global setting
      const { data: globalData, error: globalError } = await supabase
        .from('app_settings')
        .select('value')
        .eq('key', 'global_history_retention')
        .single();

      if (!globalError && globalData && globalData.value?.days) {
        return parseInt(globalData.value.days);
      }

      return 7; // Default to 7 days if no settings found
    } catch (err) {
      console.error('Error fetching history retention period:', err);
      return 7; // Default to 7 days on error
    }
  };

  // Get history for a specific monitor
  const getMonitorHistory = async (monitorId: string, limit = 100, customDays?: number): Promise<MonitorHistory[]> => {
    if (!user) return [];

    // First verify the monitor belongs to the user's company
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('id')
      .eq('id', monitorId)
      .single();

    // If we have a current company, verify the monitor belongs to it
    if (monitor && currentCompany) {
      const { data: companyMonitor, error: companyError } = await supabase
        .from('monitors')
        .select('id')
        .eq('id', monitorId)
        .eq('company_id', currentCompany.id)
        .single();

      if (companyError || !companyMonitor) {
        toast({
          title: 'Error fetching monitor history',
          description: 'Monitor not found in your company',
          variant: 'destructive',
        });
        throw new Error('Monitor not found in your company');
      }
    }

    if (monitorError || !monitor) {
      toast({
        title: 'Error fetching monitor history',
        description: 'Monitor not found or access denied',
        variant: 'destructive',
      });
      throw new Error('Monitor not found or access denied');
    }

    // Get the company's history retention period if not provided
    let retentionDays = customDays;
    if (!retentionDays && currentCompany) {
      retentionDays = await getCompanyHistoryRetention(currentCompany.id);
    }

    // Calculate the date range based on retention period
    let query = supabase
      .from('monitor_history')
      .select('*')
      .eq('monitor_id', monitorId)
      .order('timestamp', { ascending: false });

    // Apply date filter if retention period is specified
    if (retentionDays) {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - retentionDays);
      query = query.gte('timestamp', startDate.toISOString());
    }

    // Apply limit
    query = query.limit(limit);

    // Execute the query
    const { data, error } = await query;

    if (error) {
      toast({
        title: 'Error fetching monitor history',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    return data || [];
  };

  // Get recent incidents (failed checks) for a monitor
  const getMonitorIncidents = async (monitorId: string, limit = 10): Promise<MonitorHistory[]> => {
    if (!user) return [];

    // First verify the monitor belongs to the user's company
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('id')
      .eq('id', monitorId)
      .single();

    // If we have a current company, verify the monitor belongs to it
    if (monitor && currentCompany) {
      const { data: companyMonitor, error: companyError } = await supabase
        .from('monitors')
        .select('id')
        .eq('id', monitorId)
        .eq('company_id', currentCompany.id)
        .single();

      if (companyError || !companyMonitor) {
        toast({
          title: 'Error fetching monitor incidents',
          description: 'Monitor not found in your company',
          variant: 'destructive',
        });
        throw new Error('Monitor not found in your company');
      }
    }

    if (monitorError || !monitor) {
      toast({
        title: 'Error fetching monitor incidents',
        description: 'Monitor not found or access denied',
        variant: 'destructive',
      });
      throw new Error('Monitor not found or access denied');
    }

    // Then get the incidents (where status is false)
    const { data, error } = await supabase
      .from('monitor_history')
      .select('*')
      .eq('monitor_id', monitorId)
      .eq('status', false)
      .order('timestamp', { ascending: false })
      .limit(limit);

    if (error) {
      toast({
        title: 'Error fetching monitor incidents',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    return data || [];
  };

  // Calculate uptime percentage for a monitor
  const getMonitorUptime = async (monitorId: string, days = 30): Promise<number> => {
    if (!user) return 0;

    // First verify the monitor belongs to the user's company
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('id')
      .eq('id', monitorId)
      .single();

    // If we have a current company, verify the monitor belongs to it
    if (monitor && currentCompany) {
      const { data: companyMonitor, error: companyError } = await supabase
        .from('monitors')
        .select('id')
        .eq('id', monitorId)
        .eq('company_id', currentCompany.id)
        .single();

      if (companyError || !companyMonitor) {
        toast({
          title: 'Error calculating uptime',
          description: 'Monitor not found in your company',
          variant: 'destructive',
        });
        throw new Error('Monitor not found in your company');
      }
    }

    if (monitorError || !monitor) {
      toast({
        title: 'Error calculating uptime',
        description: 'Monitor not found or access denied',
        variant: 'destructive',
      });
      throw new Error('Monitor not found or access denied');
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get all checks in the period
    const { data, error } = await supabase
      .from('monitor_history')
      .select('status')
      .eq('monitor_id', monitorId)
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString());

    if (error) {
      toast({
        title: 'Error calculating uptime',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    if (!data || data.length === 0) return 100; // No data means no downtime recorded

    // Calculate percentage of successful checks
    const successfulChecks = data.filter(check => check.status).length;
    const uptimePercentage = (successfulChecks / data.length) * 100;

    return parseFloat(uptimePercentage.toFixed(2));
  };

  // React Query hooks
  const useGetMonitorHistoryQuery = (monitorId: string, limit = 100, customDays?: number) => {
    return useQuery({
      queryKey: ['monitor-history', monitorId, limit, customDays, user?.id],
      queryFn: () => getMonitorHistory(monitorId, limit, customDays),
      enabled: !!user && !!monitorId,
    });
  };

  const useGetMonitorIncidentsQuery = (monitorId: string, limit = 10) => {
    return useQuery({
      queryKey: ['monitor-incidents', monitorId, limit, user?.id],
      queryFn: () => getMonitorIncidents(monitorId, limit),
      enabled: !!user && !!monitorId,
    });
  };

  const useGetMonitorUptimeQuery = (monitorId: string, days = 30) => {
    return useQuery({
      queryKey: ['monitor-uptime', monitorId, days, user?.id],
      queryFn: () => getMonitorUptime(monitorId, days),
      enabled: !!user && !!monitorId,
    });
  };

  // Get the latest history entry for a specific monitor
  const getLatestHistoryForMonitor = async (monitorId: string): Promise<any> => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitorId)
        .order('timestamp', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        // If no data found, return null instead of throwing an error
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      // Calculate uptime for the last 24 hours
      const { data: history24h, error: error24h } = await supabase
        .from('monitor_history')
        .select('status')
        .eq('monitor_id', monitorId)
        .gte('timestamp', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .order('timestamp', { ascending: false });

      if (error24h) throw error24h;

      // Calculate uptime percentage
      const uptime_24h = history24h && history24h.length > 0
        ? (history24h.filter(h => h.status).length / history24h.length) * 100
        : undefined;

      // Calculate uptime for the last 7 days
      const { data: history7d, error: error7d } = await supabase
        .from('monitor_history')
        .select('status')
        .eq('monitor_id', monitorId)
        .gte('timestamp', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('timestamp', { ascending: false });

      if (error7d) throw error7d;

      // Calculate uptime percentage
      const uptime_7d = history7d && history7d.length > 0
        ? (history7d.filter(h => h.status).length / history7d.length) * 100
        : undefined;

      return {
        ...data,
        uptime_24h,
        uptime_7d
      };
    } catch (err) {
      console.error('Error fetching monitor history:', err);
      return null;
    }
  };

  // Get the latest history entries for multiple monitors
  const getLatestHistoryForMonitors = async (monitorIds: string[]): Promise<Record<string, any>> => {
    if (!user || !monitorIds.length) return {};

    try {
      // Get the latest entry for each monitor
      const promises = monitorIds.map(id => getLatestHistoryForMonitor(id));
      const results = await Promise.all(promises);

      // Convert to a record with monitor_id as key
      const historyMap: Record<string, any> = {};
      results.forEach(entry => {
        if (entry) {
          historyMap[entry.monitor_id] = entry;
        }
      });

      return historyMap;
    } catch (err) {
      console.error('Error fetching monitor histories:', err);
      return {};
    }
  };

  // React Query hook for getting latest history for multiple monitors
  const useGetLatestHistoryForMonitors = (monitorIds: string[]) => {
    return useQuery({
      queryKey: ['monitorHistory', 'latest', monitorIds, user?.id],
      queryFn: () => getLatestHistoryForMonitors(monitorIds),
      enabled: !!user && monitorIds.length > 0,
    });
  };

  // Get the last 4 hours of history for a monitor
  const getMonitorHistoryLast4Hours = async (monitorId: string): Promise<MonitorHistory[]> => {
    if (!user) return [];

    try {
      // Calculate date range (4 hours ago)
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 4 * 60 * 60 * 1000);

      const { data, error } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitorId)
        .gte('timestamp', startDate.toISOString())
        .lte('timestamp', endDate.toISOString())
        .order('timestamp', { ascending: true });

      if (error) throw error;

      return data || [];
    } catch (err) {
      console.error('Error fetching 4-hour monitor history:', err);
      return [];
    }
  };

  // React Query hook for getting last 4 hours of history
  const useGetMonitorHistoryLast4HoursQuery = (monitorId: string) => {
    return useQuery({
      queryKey: ['monitor-history-4h', monitorId, user?.id],
      queryFn: () => getMonitorHistoryLast4Hours(monitorId),
      enabled: !!user && !!monitorId,
    });
  };

  return {
    useGetMonitorHistoryQuery,
    useGetMonitorIncidentsQuery,
    useGetMonitorUptimeQuery,
    getLatestHistoryForMonitor,
    getLatestHistoryForMonitors,
    useGetLatestHistoryForMonitors,
    getMonitorHistoryLast4Hours,
    useGetMonitorHistoryLast4HoursQuery,
    getCompanyHistoryRetention,
  };
}
