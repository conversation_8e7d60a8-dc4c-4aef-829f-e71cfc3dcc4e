import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useCompany } from '@/contexts/CompanyContext';
import { Database } from '@/integrations/supabase/types';
import { CreateMonitorData, Monitor, UpdateMonitorData } from '@/types/monitor';
import { logAction, getClientIp, getUserAgent } from '@/utils/auditLogger';
import { useCompanyRoles } from '@/hooks/use-company-roles';

// Use the Monitor type from types/monitor.ts instead
// These are kept for reference
// type DatabaseMonitor = Database['public']['Tables']['monitors']['Row'];
// type MonitorInsert = Database['public']['Tables']['monitors']['Insert'];
// type MonitorUpdate = Database['public']['Tables']['monitors']['Update'];

export function useMonitors() {
  const { user } = useAuth();
  const { currentCompany } = useCompany();
  const companyRoles = useCompanyRoles();
  const queryClient = useQueryClient();

  // Get all monitors for the current company
  const getMonitors = async (): Promise<Monitor[]> => {
    if (!user) return [];

    try {
      // If "All Companies" is selected (for superadmins only)
      if (currentCompany?.isAllCompanies) {
        console.log('Fetching monitors for All Companies view');

        // Fetch all monitors across all companies
        let query = supabase
          .from('monitors')
          .select('*, monitor_companies(company_id, company:company_id(name))')
          .order('created_at', { ascending: false });

        try {
          // Try to exclude soft-deleted monitors
          query = query.eq('deleted', false);
        } catch (err) {
          console.warn('Could not filter by deleted status, continuing without filter');
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error fetching all monitors:', error);
          return [];
        }

        console.log(`Found ${data?.length || 0} monitors for All Companies view`);

        // Transform the data to include companies array with company names
        const result = (data || []).map(item => ({
          ...item,
          companies: (item.monitor_companies || []).map(mc => ({
            ...mc,
            company_name: mc.company?.name
          }))
        }));

        console.log('Transformed monitors:', result);
        return result;
      }

      // If no company is selected yet, try to get all monitors for the user
      if (!currentCompany) {
        let query = supabase
          .from('monitors')
          .select('*, monitor_companies(company_id)')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        try {
          // Try to exclude soft-deleted monitors
          query = query.eq('deleted', false);
        } catch (err) {
          console.warn('Could not filter by deleted status, continuing without filter');
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error fetching monitors by user_id:', error);
          return [];
        }

        // Transform the data to include companies array
        return (data || []).map(item => ({
          ...item,
          companies: item.monitor_companies || []
        }));
      }

      // If a specific company is selected, get monitors for that company through the junction table
      console.log('Fetching monitors for company:', currentCompany.id);

      try {
        // First, get all monitor IDs for this company from the junction table
        const { data: monitorCompanies, error: mcError } = await supabase
          .from('monitor_companies')
          .select('monitor_id')
          .eq('company_id', currentCompany.id);

        if (mcError) {
          console.error('Error fetching monitor_companies:', mcError);
          toast({
            title: 'Error fetching monitors',
            description: mcError.message,
            variant: 'destructive',
          });
          return [];
        }

        // If no monitors found for this company
        if (!monitorCompanies || monitorCompanies.length === 0) {
          console.log('No monitors found for company:', currentCompany.id);
          return [];
        }

        // Extract monitor IDs
        const monitorIds = monitorCompanies.map(mc => mc.monitor_id);
        console.log(`Found ${monitorIds.length} monitor IDs for company:`, currentCompany.id);

        // Now fetch the actual monitors
        let monitorsQuery = supabase
          .from('monitors')
          .select('*')
          .in('id', monitorIds);

        try {
          // Try to exclude soft-deleted monitors
          monitorsQuery = monitorsQuery.eq('deleted', false);
        } catch (err) {
          console.warn('Could not filter by deleted status, continuing without filter');
        }

        const { data: monitors, error: monitorsError } = await monitorsQuery;

        if (monitorsError) {
          console.error('Error fetching monitors:', monitorsError);
          toast({
            title: 'Error fetching monitors',
            description: monitorsError.message,
            variant: 'destructive',
          });
          return [];
        }

        if (!monitors || monitors.length === 0) {
          console.log('No active monitors found for company:', currentCompany.id);
          return [];
        }

        console.log(`Found ${monitors.length} monitors for company:`, currentCompany.id);

        // Get all company relationships for these monitors
        const { data: companiesData, error: companiesError } = await supabase
          .from('monitor_companies')
          .select('monitor_id, company_id, company:company_id(name)')
          .in('monitor_id', monitors.map(m => m.id));

        if (companiesError) {
          console.error('Error fetching monitor companies:', companiesError);
          // Continue with what we have, but without company names
        }

        // Create a map of monitor_id to companies
        const companiesMap = {};
        (companiesData || []).forEach(mc => {
          if (!companiesMap[mc.monitor_id]) {
            companiesMap[mc.monitor_id] = [];
          }
          companiesMap[mc.monitor_id].push({
            ...mc,
            company_name: mc.company?.name
          });
        });

        // Add companies to each monitor
        return monitors.map(monitor => ({
          ...monitor,
          companies: companiesMap[monitor.id] || []
        }));
      } catch (err) {
        console.error('Error in getMonitors for company:', err);
        toast({
          title: 'Error fetching monitors',
          description: err.message,
          variant: 'destructive',
        });
        return [];
      }
    } catch (err) {
      console.error('Error in getMonitors:', err);
      return [];
    }
  };

  // Get a single monitor by ID
  const getMonitor = async (id: string): Promise<Monitor | null> => {
    if (!user) return null;

    try {
      // First, check if the user is associated with any companies that have this monitor
      // This approach works for both company admins and regular users
      const { data: monitorCompanies, error: mcError } = await supabase
        .from('monitor_companies')
        .select('monitor_id, company_id, company:company_id(name)')
        .eq('monitor_id', id);

      if (mcError) {
        console.error('Error fetching monitor companies:', mcError);
        // Continue with direct monitor fetch as fallback
      }

      // If we found company associations, check if the user is a member of any of these companies
      if (monitorCompanies && monitorCompanies.length > 0) {
        const companyIds = monitorCompanies.map(mc => mc.company_id);

        // Check if the user is a member of any of these companies
        const { data: userCompanies, error: ucError } = await supabase
          .from('company_members')
          .select('company_id')
          .eq('user_id', user.id)
          .in('company_id', companyIds);

        if (ucError) {
          console.error('Error checking user company membership:', ucError);
          // Continue with direct monitor fetch as fallback
        }

        // If the user is a member of at least one company that has this monitor, fetch the monitor
        if (userCompanies && userCompanies.length > 0) {
          let query = supabase
            .from('monitors')
            .select('*')
            .eq('id', id);

          try {
            // Try to exclude soft-deleted monitors
            query = query.eq('deleted', false);
          } catch (err) {
            console.warn('Could not filter by deleted status, continuing without filter');
          }

          const { data: monitor, error: monitorError } = await query.single();

          if (monitorError) {
            toast({
              title: 'Error fetching monitor',
              description: monitorError.message,
              variant: 'destructive',
            });
            throw monitorError;
          }

          // Return the monitor with its company associations
          return {
            ...monitor,
            companies: monitorCompanies.map(mc => ({
              ...mc,
              company_name: mc.company?.name
            }))
          };
        }
      }

      // Fallback: Try to fetch the monitor directly (for monitors created by this user)
      let query = supabase
        .from('monitors')
        .select('*, monitor_companies(company_id, company:company_id(name))')
        .eq('id', id)
        .eq('user_id', user.id);

      try {
        // Try to exclude soft-deleted monitors
        query = query.eq('deleted', false);
      } catch (err) {
        console.warn('Could not filter by deleted status, continuing without filter');
      }

      const { data, error } = await query.single();

      if (error) {
        toast({
          title: 'Error fetching monitor',
          description: error.message,
          variant: 'destructive',
        });
        throw error;
      }

      // Transform the data to include companies array with company names
      return {
        ...data,
        companies: (data.monitor_companies || []).map(mc => ({
          ...mc,
          company_name: mc.company?.name
        }))
      };
    } catch (error) {
      console.error('Error in getMonitor:', error);
      throw error;
    }
  };

  // Create a new monitor
  const createMonitor = async (monitorData: CreateMonitorData): Promise<Monitor> => {
    if (!user) throw new Error('User not authenticated');

    // Start a Supabase transaction
    const { data, error } = await supabase.rpc('create_monitor_with_companies', {
      monitor_name: monitorData.name,
      monitor_target: monitorData.target,
      monitor_type: monitorData.type,
      monitor_interval: monitorData.interval,
      monitor_timeout: monitorData.timeout,
      user_id: user.id,
      company_ids: monitorData.company_ids
    });

    if (error) {
      console.error('Error creating monitor with companies:', error);

      // Fallback to manual creation if the RPC fails
      try {
        // First create the monitor
        const { data: monitorData, error: monitorError } = await supabase
          .from('monitors')
          .insert({
            name: monitorData.name,
            target: monitorData.target,
            type: monitorData.type,
            interval: monitorData.interval,
            timeout: monitorData.timeout,
            user_id: user.id,
            active: true,
            // No company_id needed since we're using the junction table
          })
          .select()
          .single();

        if (monitorError) throw monitorError;

        // Then create the monitor-company relationships
        if (monitorData.company_ids && monitorData.company_ids.length > 0) {
          const monitorCompanies = monitorData.company_ids.map(companyId => ({
            monitor_id: monitorData.id,
            company_id: companyId
          }));

          const { error: relationError } = await supabase
            .from('monitor_companies')
            .insert(monitorCompanies);

          if (relationError) throw relationError;
        }

        toast({
          title: 'Monitor created',
          description: `Monitor for ${monitorData.name} has been created successfully.`,
        });

        return monitorData;
      } catch (err) {
        // Provide more specific error messages
        let errorMessage = err.message;

        // Check for specific error types
        if (errorMessage.includes('Monitor limit reached')) {
          errorMessage = 'Monitor limit reached for this company. Please upgrade your subscription to add more monitors.';
        } else if (errorMessage.includes('violates foreign key constraint')) {
          errorMessage = 'Invalid company selected. Please choose a valid company.';
        } else if (errorMessage.includes('permission denied')) {
          errorMessage = 'You do not have permission to create monitors for this company.';
        }

        toast({
          title: 'Error creating monitor',
          description: errorMessage,
          variant: 'destructive',
        });

        // Rethrow with the improved message
        const enhancedError = new Error(errorMessage);
        enhancedError.originalError = err;
        throw enhancedError;
      }
    }

    // Handle degraded settings if provided
    if (monitorData.degraded_settings && data) {
      try {
        // Use the RPC function to create degraded settings
        const { error: settingsError } = await supabase.rpc(
          'upsert_monitor_degraded_settings',
          {
            p_monitor_id: data.id,
            p_response_time: monitorData.degraded_settings.response_time,
            p_error_rate: monitorData.degraded_settings.error_rate,
            p_status_codes: monitorData.degraded_settings.status_codes,
            p_consecutive_failures: monitorData.degraded_settings.consecutive_failures
          }
        );

        if (settingsError) {
          console.error('Error creating degraded settings:', settingsError);
        }
      } catch (settingsError) {
        console.error('Error handling degraded settings:', settingsError);
      }
    }

    // Log the action
    await logAction(
      user.id,
      'create',
      'monitors',
      data.id,
      null,
      data,
      getClientIp(),
      getUserAgent()
    );

    toast({
      title: 'Monitor created',
      description: `Monitor for ${monitorData.name} has been created successfully.`,
    });

    return data;
  };

  // Update an existing monitor
  const updateMonitor = async ({ id, company_ids, degraded_settings, ...updates }: UpdateMonitorData & { id: string }): Promise<Monitor> => {
    if (!user) throw new Error('User not authenticated');

    // Variable to store the final monitor data
    let monitorResult: Monitor;

    try {
      console.log('Updating monitor with ID:', id);
      console.log('Updates:', updates);
      console.log('Company IDs:', company_ids);

      // Use the RPC function to update the monitor and its company associations
      const { data, error } = await supabase.rpc('update_monitor_with_companies_v2', {
        monitor_id: id,
        monitor_name: updates.name,
        monitor_target: updates.target,
        monitor_type: updates.type,
        monitor_interval: updates.interval,
        monitor_timeout: updates.timeout,
        monitor_active: updates.active,
        company_ids: company_ids || []
      });

      if (error) {
        console.error('Error updating monitor with RPC:', error);
        throw error;
      }

      console.log('Monitor updated successfully with RPC:', data);
      monitorResult = data;
    } catch (err) {
      console.error('Error in updateMonitor:', err);

      // Provide more specific error messages
      let errorMessage = err.message || 'An error occurred while updating the monitor';

      // Check for specific error types
      if (errorMessage.includes('Monitor limit reached')) {
        errorMessage = 'Monitor limit reached for this company. Please upgrade your subscription to add more monitors.';
      } else if (errorMessage.includes('violates foreign key constraint')) {
        errorMessage = 'Invalid company selected. Please choose a valid company.';
      } else if (errorMessage.includes('permission denied')) {
        errorMessage = 'You do not have permission to update monitors for this company.';
      }

      toast({
        title: 'Error updating monitor',
        description: errorMessage,
        variant: 'destructive',
      });

      // Rethrow with the improved message
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = err;
      throw enhancedError;
    }

    // Handle degraded settings if provided
    if (degraded_settings !== undefined) {
      try {
        if (degraded_settings === null) {
          // Delete monitor-specific degraded settings
          const { error: deleteError } = await supabase
            .from('monitor_degraded_settings')
            .delete()
            .eq('monitor_id', id);

          if (deleteError) {
            console.error('Error deleting degraded settings:', deleteError);
          }
        } else {
          // Upsert monitor-specific degraded settings using the RPC function
          const { error: upsertError } = await supabase.rpc(
            'upsert_monitor_degraded_settings',
            {
              p_monitor_id: id,
              p_response_time: degraded_settings.response_time,
              p_error_rate: degraded_settings.error_rate,
              p_status_codes: degraded_settings.status_codes,
              p_consecutive_failures: degraded_settings.consecutive_failures
            }
          );

          if (upsertError) {
            console.error('Error upserting degraded settings:', upsertError);
          }
        }
      } catch (settingsError) {
        console.error('Error handling degraded settings:', settingsError);
      }
    }

    // Make sure monitorResult is set
    if (!monitorResult) {
      // If we don't have a result yet, fetch the monitor data
      const { data: fetchedMonitor, error: fetchError } = await supabase
        .from('monitors')
        .select(`
          *,
          companies:monitor_companies(
            id,
            company_id,
            created_at
          )
        `)
        .eq('id', id)
        .single();

      if (fetchError) {
        console.error('Error fetching updated monitor data:', fetchError);
        throw fetchError;
      }

      monitorResult = fetchedMonitor;
    }

    // Get the original monitor data for audit log
    const { data: originalMonitor } = await supabase
      .from('monitors')
      .select('*')
      .eq('id', id)
      .single();

    // Log the action
    await logAction(
      user.id,
      'update',
      'monitors',
      id,
      originalMonitor,
      monitorResult,
      getClientIp(),
      getUserAgent()
    );

    toast({
      title: 'Monitor updated',
      description: `Monitor has been updated successfully.`,
    });

    return monitorResult;
  };

  // Soft delete a monitor (for admins)
  const softDeleteMonitor = async (id: string): Promise<Monitor> => {
    if (!user) throw new Error('User not authenticated');

    try {
      console.log(`Attempting to soft delete monitor with ID: ${id}`);

      // First, check if the user has permission to delete this monitor
      // Check if the user is an admin in any company associated with this monitor
      const { data: monitorCompanies, error: mcError } = await supabase
        .from('monitor_companies')
        .select('company_id')
        .eq('monitor_id', id);

      if (mcError) {
        console.error('Error fetching monitor companies:', mcError);
        throw new Error(`Failed to check monitor companies: ${mcError.message}`);
      }

      console.log(`Found ${monitorCompanies?.length || 0} companies for monitor ${id}`);

      // Check if the user is an admin in any of these companies
      let hasAdminAccess = false;
      if (monitorCompanies && monitorCompanies.length > 0) {
        const companyIds = monitorCompanies.map(mc => mc.company_id);

        const { data: userRoles, error: rolesError } = await supabase
          .from('company_members')
          .select('company_id, role_type')
          .eq('user_id', user.id)
          .in('company_id', companyIds);

        if (rolesError) {
          console.error('Error checking user roles:', rolesError);
          throw new Error(`Failed to check user roles: ${rolesError.message}`);
        }

        console.log(`User roles in monitor companies:`, userRoles);

        // Check if user is admin or superadmin in any of these companies
        hasAdminAccess = (userRoles || []).some(role =>
          ['admin', 'superadmin'].includes(role.role_type)
        );
      }

      // Also check if the user is the creator of the monitor
      const { data: monitorData, error: monitorError } = await supabase
        .from('monitors')
        .select('user_id')
        .eq('id', id)
        .single();

      if (monitorError) {
        console.error('Error fetching monitor data:', monitorError);
        throw new Error(`Failed to fetch monitor data: ${monitorError.message}`);
      }

      const isCreator = monitorData?.user_id === user.id;
      console.log(`User is creator of monitor: ${isCreator}`);

      // If the user doesn't have permission, throw an error
      if (!hasAdminAccess && !isCreator) {
        throw new Error('You do not have permission to delete this monitor. You must be an admin of a company that owns this monitor or the creator of the monitor.');
      }

      // Call the soft_delete_monitor function
      console.log('Calling soft_delete_monitor RPC function');
      const { data, error } = await supabase.rpc('soft_delete_monitor', {
        p_monitor_id: id,
        p_user_id: user.id
      });

      if (error) {
        console.error('Error from soft_delete_monitor RPC:', error);

        // Provide more specific error messages based on the error
        let errorMessage = error.message;
        if (errorMessage.includes('permission')) {
          errorMessage = 'You do not have permission to delete this monitor. You must be an admin of a company that owns this monitor.';
        } else if (errorMessage.includes('not found')) {
          errorMessage = 'Monitor not found or already deleted.';
        }

        toast({
          title: 'Error deleting monitor',
          description: errorMessage,
          variant: 'destructive',
        });

        throw new Error(`Failed to soft delete monitor: ${errorMessage}`);
      }

      console.log('Monitor soft deleted successfully:', data);

      // Get the original monitor data for audit log
      const { data: originalMonitor } = await supabase
        .from('monitors')
        .select('*')
        .eq('id', id)
        .single();

      // Log the action
      await logAction(
        user.id,
        'soft_delete',
        'monitors',
        id,
        originalMonitor,
        null,
        getClientIp(),
        getUserAgent()
      );

      toast({
        title: 'Monitor deleted',
        description: 'The monitor has been deleted successfully.',
      });

      return data;
    } catch (error) {
      console.error('Error in softDeleteMonitor:', error);

      // Show a toast with the error message
      toast({
        title: 'Error deleting monitor',
        description: error.message || 'An unexpected error occurred while deleting the monitor.',
        variant: 'destructive',
      });

      throw error;
    }
  };

  // Restore a soft-deleted monitor (for admins)
  const restoreMonitor = async (id: string): Promise<Monitor> => {
    if (!user) throw new Error('User not authenticated');

    // Call the restore_monitor function
    const { data, error } = await supabase.rpc('restore_monitor', {
      p_monitor_id: id,
      p_user_id: user.id
    });

    if (error) {
      toast({
        title: 'Error restoring monitor',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    // Log the action
    await logAction(
      user.id,
      'restore',
      'monitors',
      id,
      null,
      data,
      getClientIp(),
      getUserAgent()
    );

    toast({
      title: 'Monitor restored',
      description: 'The monitor has been restored successfully.',
    });

    return data;
  };

  // Hard delete a monitor (for superadmins only)
  const hardDeleteMonitor = async (id: string): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    // Check if the user is a superadmin
    const isSuperadmin = await companyRoles.isGlobalSuperadmin();

    if (!isSuperadmin) {
      toast({
        title: 'Access Denied',
        description: 'Only superadmins can permanently delete monitors',
        variant: 'destructive',
      });
      throw new Error('Only superadmins can permanently delete monitors');
    }

    // Get the monitor data before deletion for audit log
    const { data: monitorData, error: getError } = await supabase
      .from('monitors')
      .select('*')
      .eq('id', id)
      .single();

    if (getError) {
      console.error('Error fetching monitor data for deletion:', getError);
    }

    const { error } = await supabase
      .from('monitors')
      .delete()
      .eq('id', id);

    if (error) {
      toast({
        title: 'Error permanently deleting monitor',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    // Log the action
    await logAction(
      user.id,
      'delete',
      'monitors',
      id,
      monitorData,
      null,
      getClientIp(),
      getUserAgent()
    );

    toast({
      title: 'Monitor permanently deleted',
      description: 'The monitor has been permanently deleted.',
    });
  };

  // Toggle monitor active status
  const toggleMonitorStatus = async (id: string, active: boolean): Promise<Monitor> => {
    if (!user) throw new Error('User not authenticated');

    try {
      // First, check if the user is associated with any companies that have this monitor
      // and if they have admin rights in any of those companies
      const { data: monitorCompanies, error: mcError } = await supabase
        .from('monitor_companies')
        .select('company_id')
        .eq('monitor_id', id);

      if (mcError) {
        console.error('Error fetching monitor companies:', mcError);
        // Continue with direct monitor check as fallback
      }

      let hasAccess = false;

      // If we found company associations, check if the user is an admin in any of these companies
      if (monitorCompanies && monitorCompanies.length > 0) {
        const companyIds = monitorCompanies.map(mc => mc.company_id);

        // Check if the user is an admin in any of these companies
        const { data: userCompanies, error: ucError } = await supabase
          .from('company_members')
          .select('company_id, role_type')
          .eq('user_id', user.id)
          .in('company_id', companyIds)
          .in('role_type', ['admin', 'superadmin']);

        if (ucError) {
          console.error('Error checking user admin status:', ucError);
          // Continue with direct monitor check as fallback
        }

        // If the user is an admin in at least one company that has this monitor, they have access
        if (userCompanies && userCompanies.length > 0) {
          hasAccess = true;
        }
      }

      // If the user doesn't have access through company admin rights, check if they're the creator
      if (!hasAccess) {
        // Check if the user is the creator of the monitor
        const { data: checkData, error: checkError } = await supabase
          .from('monitors')
          .select('id')
          .eq('id', id)
          .eq('user_id', user.id)
          .single();

        if (checkError) {
          toast({
            title: `Error ${active ? 'activating' : 'pausing'} monitor`,
            description: 'Monitor not found or you do not have permission to modify it.',
            variant: 'destructive',
          });
          throw checkError;
        }

        hasAccess = true;
      }

      // If the user has access, update the monitor status
      if (hasAccess) {
        const { data, error } = await supabase
          .from('monitors')
          .update({ active })
          .eq('id', id)
          .select()
          .single();

        if (error) {
          toast({
            title: `Error ${active ? 'activating' : 'pausing'} monitor`,
            description: error.message,
            variant: 'destructive',
          });
          throw error;
        }

        // Log the action
        await logAction(
          user.id,
          'update_status',
          'monitors',
          id,
          { id, active: !active },
          { id, active },
          getClientIp(),
          getUserAgent()
        );

        toast({
          title: active ? 'Monitor activated' : 'Monitor paused',
          description: `The monitor has been ${active ? 'activated' : 'paused'} successfully.`,
        });

        return data;
      } else {
        // This should not happen given the checks above, but just in case
        throw new Error('You do not have permission to modify this monitor');
      }
    } catch (error) {
      console.error('Error in toggleMonitorStatus:', error);
      throw error;
    }
  };

  // React Query hooks
  const useGetMonitorsQuery = () => {
    return useQuery({
      queryKey: ['monitors', currentCompany?.id, user?.id],
      queryFn: getMonitors,
      enabled: !!user, // Allow query to run even without a company
      // Add staleTime and refetchOnWindowFocus options to make the query more responsive
      staleTime: 10 * 1000, // Consider data stale after 10 seconds
      refetchOnWindowFocus: true, // Refetch when window regains focus
    });
  };

  const useGetMonitorQuery = (id: string) => {
    return useQuery({
      queryKey: ['monitor', id, user?.id],
      queryFn: () => getMonitor(id),
      enabled: !!user && !!id,
    });
  };

  const useCreateMonitorMutation = () => {
    return useMutation({
      mutationFn: createMonitor,
      onSuccess: () => {
        // Invalidate all monitor-related queries to ensure the UI updates
        queryClient.invalidateQueries({ queryKey: ['monitors'] });

        // Log that we're invalidating queries
        console.log('Invalidating monitor queries after creating a new monitor');
      },
    });
  };

  const useUpdateMonitorMutation = () => {
    return useMutation({
      mutationFn: updateMonitor,
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ['monitors', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['monitor', data.id, user?.id] });
      },
    });
  };

  const useSoftDeleteMonitorMutation = () => {
    return useMutation({
      mutationFn: softDeleteMonitor,
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ['monitors', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['monitor', data.id, user?.id] });
      },
      onError: (error) => {
        console.error('Error in useSoftDeleteMonitorMutation:', error);
        // Toast is already shown in the softDeleteMonitor function
      }
    });
  };

  const useRestoreMonitorMutation = () => {
    return useMutation({
      mutationFn: restoreMonitor,
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ['monitors', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['monitor', data.id, user?.id] });
      },
    });
  };

  const useHardDeleteMonitorMutation = () => {
    return useMutation({
      mutationFn: hardDeleteMonitor,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['monitors', user?.id] });
      },
    });
  };

  const useToggleMonitorStatusMutation = () => {
    return useMutation({
      mutationFn: ({ id, active }: { id: string; active: boolean }) => toggleMonitorStatus(id, active),
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ['monitors', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['monitor', data.id, user?.id] });
      },
    });
  };

  return {
    useGetMonitorsQuery,
    useGetMonitorQuery,
    useCreateMonitorMutation,
    useUpdateMonitorMutation,
    useSoftDeleteMonitorMutation,
    useRestoreMonitorMutation,
    useHardDeleteMonitorMutation,
    useToggleMonitorStatusMutation,
  };
}
