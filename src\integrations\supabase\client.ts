// Supabase client configuration
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

/**
 * Centralized Supabase client for the application
 *
 * This is the only instance of the Supabase client that should be used throughout the application.
 * Import it like this:
 * import { supabase } from "@/integrations/supabase/client";
 */

// Get backend mode configuration
const backendMode = import.meta.env.VITE_BACKEND_MODE || 'local';

// Determine which Supabase configuration to use based on backend mode
let url: string;
let key: string;

if (backendMode === 'external') {
  // Use external backend configuration
  url = import.meta.env.VITE_EXTERNAL_SUPABASE_URL;
  key = import.meta.env.VITE_EXTERNAL_SUPABASE_ANON_KEY;

  if (!url || !key) {
    console.error(
      'ERROR: External Supabase credentials are not set. ' +
      'Please set VITE_EXTERNAL_SUPABASE_URL and VITE_EXTERNAL_SUPABASE_ANON_KEY in your .env.local file.'
    );
    throw new Error('External Supabase credentials are not set');
  }

  console.log('🌐 Using EXTERNAL backend:', url);
} else {
  // Use local backend configuration (default)
  url = import.meta.env.VITE_SUPABASE_URL;
  key = import.meta.env.VITE_SUPABASE_ANON_KEY;

  if (!url || !key) {
    console.error(
      'ERROR: Local Supabase credentials are not set. ' +
      'Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env.local file.'
    );
    throw new Error('Local Supabase credentials are not set');
  }

  console.log('🏠 Using LOCAL backend:', url);
}

// Create the Supabase client
export const supabase = createClient<Database>(url, key);

// Export backend mode for debugging
export const currentBackendMode = backendMode;