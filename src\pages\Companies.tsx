import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCompany } from '@/contexts/CompanyContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Building, Plus, Settings, Users, ArrowRight, RefreshCw, Shield } from 'lucide-react';
import AppLayout from '@/components/AppLayout';
import UnifiedHeader from '@/components/UnifiedHeader';
import DocumentTitle from '@/components/DocumentTitle';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { useCompanyRoles } from '@/hooks/use-company-roles';

const Companies = () => {
  const navigate = useNavigate();
  const { companies, fetchCompanies, isLoading, getUserRole, setCurrentCompany } = useCompany();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin = false, isLoading: isSuperadminLoading } = useGlobalSuperadminQuery();

  // Use a ref to track if we've already fetched companies on this page load
  const hasLoadedRef = React.useRef(false);

  useEffect(() => {
    if (!hasLoadedRef.current) {
      // Only force fetch companies once when the Companies page first loads
      console.log('Companies page: Initial load - forcing fetch of companies');
      fetchCompanies(true);
      hasLoadedRef.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelectCompany = (company: any) => {
    setCurrentCompany(company);
    navigate('/dashboard');
  };

  const header = (
    <UnifiedHeader
      title="My Companies"
      icon={Building}
      actions={
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => fetchCompanies(true)} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
          {isSuperadmin && (
            <Button onClick={() => navigate('/create-company')}>
              <Plus className="h-4 w-4 mr-2" />
              Create Company
            </Button>
          )}
        </div>
      }
    />
  );

  return (
    <AppLayout header={header}>
      <DocumentTitle title="My Companies" />
      <div className="container mx-auto py-8 px-4">

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-full" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-1/2 mb-4" />
                  <Skeleton className="h-4 w-1/3" />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Skeleton className="h-9 w-20" />
                  <Skeleton className="h-9 w-20" />
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : companies.length === 0 ? (
          <Card className="text-center p-8">
            <div className="flex flex-col items-center justify-center space-y-4">
              <Building className="h-16 w-16 text-slate-300 dark:text-slate-600" />
              <h2 className="text-2xl font-semibold">No Companies Found</h2>
              <p className="text-slate-500 dark:text-slate-400 max-w-md mx-auto">
                {isSuperadmin
                  ? "You don't have any companies yet. Create your first company to start managing monitors."
                  : "You don't have any companies yet. Please contact a superadmin to create a company for you."}
              </p>
              {isSuperadmin ? (
                <Button onClick={() => navigate('/create-company')} className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Company
                </Button>
              ) : (
                <div className="flex items-center mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-md text-blue-700 dark:text-blue-300">
                  <Shield className="h-5 w-5 mr-2 text-blue-500" />
                  <span className="text-sm">Only superadmins can create companies</span>
                </div>
              )}
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {companies.map((company) => {
              const role = getUserRole(company.id);
              return (
                <Card key={company.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-xl">{company.name}</CardTitle>
                      <Badge variant={role === 'admin' ? 'default' : role === 'member' ? 'secondary' : 'outline'}>
                        {role === 'admin' ? 'Admin' :
                         role === 'member' ? 'Member' :
                         role === 'user' ? 'User' :
                         role || 'User'}
                      </Badge>
                    </div>
                    <CardDescription>
                      {company.description || 'No description provided'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
                      <Users className="h-4 w-4 mr-1" />
                      <span>Created {new Date(company.created_at).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" size="sm" onClick={() => handleSelectCompany(company)}>
                      Select
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                    {(role === 'admin' || isSuperadmin) && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate(`/company/${company.id}/settings`)}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Manage
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default Companies;
