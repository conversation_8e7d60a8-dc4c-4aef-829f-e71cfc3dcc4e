import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useCompany } from '@/contexts/CompanyContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Building, Users, Trash2, Settings } from 'lucide-react';
import AppLayout from '@/components/AppLayout';
import UnifiedHeader from '@/components/UnifiedHeader';
import DocumentTitle from '@/components/DocumentTitle';
import { toast } from '@/components/ui/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CompanyMember, CompanyRole, UserRole } from '@/types/company';
import { RoleSelector } from '@/components/RoleSelector';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import CompanyHistoryRetentionSettings from '@/components/CompanyHistoryRetentionSettings';
import CompanySubscriptionTierSelector from '@/components/CompanySubscriptionTierSelector';
import { CompanyMonitorAssignment } from '@/components/CompanyMonitorAssignment';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

const CompanySettings = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    companies,
    companyMembers,
    fetchCompanyMembers,
    updateCompany,
    deleteCompany,
    addCompanyMember,
    updateCompanyMember,
    removeCompanyMember,
    isAdmin,
  } = useCompany();

  // New role management hooks
  const {
    isSuperadmin,
    isAdmin: isRoleAdmin,
    isGlobalSuperadmin,
    useUpdateUserRoleMutation,
    useRemoveCompanyMemberMutation,
    useGlobalSuperadminQuery
  } = useCompanyRoles();
  const updateUserRole = useUpdateUserRoleMutation();
  const removeMember = useRemoveCompanyMemberMutation();
  const { data: isUserGlobalSuperadmin } = useGlobalSuperadminQuery();

  const [isLoading, setIsLoading] = useState(false);
  const [company, setCompany] = useState(companies.find((c) => c.id === id) || null);
  const [formData, setFormData] = useState({
    name: company?.name || '',
    description: company?.description || '',
  });
  const [userIsSuperadmin, setUserIsSuperadmin] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [newMemberRole, setNewMemberRole] = useState<CompanyRole>('member');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);

  // Redirect if not an admin or superadmin
  useEffect(() => {
    if (!isLoading && !isAdmin && !isUserGlobalSuperadmin) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to manage this company',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }
  }, [isAdmin, isLoading, navigate, isUserGlobalSuperadmin]);

  // Update company state when companies change
  useEffect(() => {
    const foundCompany = companies.find((c) => c.id === id);
    if (foundCompany) {
      setCompany(foundCompany);
      setFormData({
        name: foundCompany.name,
        description: foundCompany.description || '',
      });
    }
  }, [companies, id]);

  // Fetch company members
  useEffect(() => {
    if (id) {
      fetchCompanyMembers(id);
    }
  }, [id, fetchCompanyMembers]);

  // Check if the user is a superadmin
  useEffect(() => {
    const checkSuperadmin = async () => {
      if (user) {
        // First check if the user is a global superadmin
        const isGlobalSuperadminResult = await isGlobalSuperadmin();

        if (isGlobalSuperadminResult) {
          setUserIsSuperadmin(true);
          return;
        }

        // If not a global superadmin, check if they're a superadmin in this company
        if (id) {
          const isSuperadminResult = await isSuperadmin(id);
          setUserIsSuperadmin(isSuperadminResult);
        }
      }
    };

    checkSuperadmin();
  }, [id, user, isSuperadmin, isGlobalSuperadmin]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleUpdateCompany = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast({
        title: 'Error',
        description: 'Company name is required',
        variant: 'destructive',
      });
      return;
    }

    if (!id) return;

    setIsLoading(true);

    try {
      await updateCompany(id, {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCompany = async () => {
    if (!id) return;

    setIsLoading(true);

    try {
      const success = await deleteCompany(id);
      if (success) {
        navigate('/dashboard');
      }
    } finally {
      setIsLoading(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleSearchUser = async (email: string) => {
    if (!email.trim()) {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);

    try {
      console.log('Searching for users with email:', email);

      // Use the stored procedure to search for users
      const { data, error } = await supabase
        .rpc('search_users', { search_email: email });

      if (error) {
        console.error('Error from Supabase:', error);
        throw error;
      }

      console.log('Search results:', data);

      // Filter out users who are already members
      const existingMemberIds = companyMembers.map(member => member.user_id);
      const filteredResults = data.filter(user => !existingMemberIds.includes(user.id));

      console.log('Filtered results:', filteredResults);
      setSearchResults(filteredResults);
    } catch (err) {
      console.error('Error searching users:', err);
      toast({
        title: 'Error',
        description: 'Failed to search users: ' + (err.message || err),
        variant: 'destructive',
      });
    } finally {
      setSearchLoading(false);
    }
  };

  const handleAddMember = async (userId: string) => {
    if (!id) return;

    setIsLoading(true);

    try {
      await addCompanyMember({
        company_id: id,
        user_id: userId,
        role_type: newMemberRole === 'admin' ? 'admin' : 'user',
      });

      setAddMemberDialogOpen(false);
      setNewMemberEmail('');
      setNewMemberRole('member');
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateMemberRole = async (memberId: string, role: CompanyRole) => {
    setIsLoading(true);

    try {
      await updateCompanyMember(memberId, {
        role_type: role === 'admin' ? 'admin' : 'user'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    setIsLoading(true);

    try {
      await removeCompanyMember(memberId);
    } finally {
      setIsLoading(false);
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = (name: string, email: string) => {
    if (name) {
      const nameParts = name.split(' ');
      if (nameParts.length > 1) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return name[0].toUpperCase();
    }

    return email ? email[0].toUpperCase() : 'U';
  };

  if (!company) {
    const notFoundHeader = (
      <UnifiedHeader
        title="Company Not Found"
        showCompanySelector={false}
        actions={
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/dashboard')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        }
      />
    );

    return (
      <AppLayout header={notFoundHeader}>
        <DocumentTitle title="Company Not Found" />
        <div className="flex items-center justify-center h-[calc(100vh-64px)]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Company Not Found</h2>
            <p className="text-slate-500 dark:text-slate-400 mb-4">The company you're looking for doesn't exist or you don't have access to it.</p>
            <Button onClick={() => navigate('/companies')}>Go to Companies</Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  const header = (
    <UnifiedHeader
      title={`${company.name} Settings`}
      icon={Settings}
      showCompanySelector={false}
      actions={
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      }
    />
  );

  return (
    <AppLayout header={header}>
      <DocumentTitle title={`${company.name} - Settings`} />
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-3xl mx-auto">

          <Tabs defaultValue="general" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">
                <Building className="h-4 w-4 mr-2" />
                General
              </TabsTrigger>
              <TabsTrigger value="members">
                <Users className="h-4 w-4 mr-2" />
                Members
              </TabsTrigger>
              {userIsSuperadmin && (
                <TabsTrigger value="monitors">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2">
                    <rect width="20" height="14" x="2" y="3" rx="2" />
                    <line x1="8" x2="16" y1="21" y2="21" />
                    <line x1="12" x2="12" y1="17" y2="21" />
                  </svg>
                  Monitors
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="general">
              <Card>
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                  <CardDescription>
                    Update your company details
                  </CardDescription>
                </CardHeader>
                <form onSubmit={handleUpdateCompany}>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Company Name *</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="Enter company name"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        placeholder="Enter company description (optional)"
                        rows={3}
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                      <DialogTrigger asChild>
                        <Button type="button" variant="destructive">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Company
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Delete Company</DialogTitle>
                          <DialogDescription>
                            Are you sure you want to delete this company? This action cannot be undone.
                            All monitors associated with this company will be deleted.
                          </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                          <Button
                            variant="outline"
                            onClick={() => setDeleteDialogOpen(false)}
                            disabled={isLoading}
                          >
                            Cancel
                          </Button>
                          <Button
                            variant="destructive"
                            onClick={handleDeleteCompany}
                            disabled={isLoading}
                          >
                            {isLoading ? 'Deleting...' : 'Delete Company'}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                    <Button type="submit" disabled={isLoading}>
                      {isLoading ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </CardFooter>
                </form>
              </Card>

              {/* Subscription Tier Settings */}
              {userIsSuperadmin && (
                <div className="mt-6">
                  <CompanySubscriptionTierSelector
                    companyId={id!}
                    companyName={company?.name}
                    isSuperadmin={userIsSuperadmin}
                  />
                </div>
              )}

              {/* History Retention Settings - Deprecated, now handled in subscription settings */}
              {false && userIsSuperadmin && (
                <div className="mt-6">
                  <CompanyHistoryRetentionSettings
                    companyId={id!}
                    companyName={company?.name}
                    isSuperadmin={userIsSuperadmin}
                  />
                </div>
              )}
            </TabsContent>

            <TabsContent value="members">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Company Members</CardTitle>
                      <CardDescription>
                        Manage members of your company
                      </CardDescription>
                    </div>
                    <Dialog open={addMemberDialogOpen} onOpenChange={setAddMemberDialogOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm">
                          Add Member
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add Company Member</DialogTitle>
                          <DialogDescription>
                            Search for a user by email to add them to your company.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                          <div className="space-y-2">
                            <Label htmlFor="email">Email</Label>
                            <Input
                              id="email"
                              value={newMemberEmail}
                              onChange={(e) => {
                                setNewMemberEmail(e.target.value);
                                handleSearchUser(e.target.value);
                              }}
                              placeholder="Search by email"
                            />
                          </div>
                          {searchLoading && <p className="text-sm text-slate-500">Searching...</p>}
                          {searchResults.length > 0 && (
                            <div className="space-y-2">
                              <Label>Search Results</Label>
                              <div className="border rounded-md divide-y">
                                {searchResults.map((user) => (
                                  <div
                                    key={user.id}
                                    className="p-2 flex items-center justify-between hover:bg-slate-100 dark:hover:bg-slate-800"
                                  >
                                    <div className="flex items-center">
                                      <Avatar className="h-8 w-8 mr-2">
                                        {user.avatar_url ? (
                                          <AvatarImage src={user.avatar_url} alt={user.full_name || user.email} />
                                        ) : null}
                                        <AvatarFallback>
                                          {getUserInitials(user.full_name || '', user.email)}
                                        </AvatarFallback>
                                      </Avatar>
                                      <div>
                                        {user.full_name && <p className="text-sm font-medium">{user.full_name}</p>}
                                        <p className="text-xs text-slate-500">{user.email}</p>
                                      </div>
                                    </div>
                                    <Button
                                      size="sm"
                                      onClick={() => handleAddMember(user.id)}
                                      disabled={isLoading}
                                    >
                                      Add
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          {!searchLoading && newMemberEmail && searchResults.length === 0 && (
                            <p className="text-sm text-slate-500">No users found</p>
                          )}
                          <div className="space-y-2">
                            <Label htmlFor="role">Role</Label>
                            <Select
                              value={newMemberRole}
                              onValueChange={(value) => setNewMemberRole(value as CompanyRole)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="admin">Admin</SelectItem>
                                <SelectItem value="member">User</SelectItem>
                              </SelectContent>
                            </Select>
                            <p className="text-xs text-slate-500">
                              Admins can manage the company and monitors. Users can view monitors but cannot modify them.
                            </p>
                            <div className="mt-4">
                              <Label htmlFor="new-role-type">New Role System</Label>
                              <div className="flex items-center space-x-2 mt-2">
                                <div className="flex-1">
                                  <Select
                                    disabled={!userIsSuperadmin}
                                    value={newMemberRole === 'admin' ? 'admin' : 'user'}
                                    onValueChange={(value) => {
                                      if (value === 'admin') {
                                        setNewMemberRole('admin');
                                      } else {
                                        setNewMemberRole('member');
                                      }
                                    }}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select role type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="user">User</SelectItem>
                                      <SelectItem value="admin">Admin</SelectItem>
                                      {userIsSuperadmin && (
                                        <SelectItem value="superadmin">Superadmin</SelectItem>
                                      )}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                              <p className="text-xs text-slate-500 mt-1">
                                New role system: Users can view monitors, Admins can manage monitors and company members, Superadmins have full control.
                                {!userIsSuperadmin && ' Only superadmins can assign the superadmin role.'}
                              </p>
                            </div>
                          </div>
                        </div>
                        <DialogFooter>
                          <Button
                            variant="outline"
                            onClick={() => setAddMemberDialogOpen(false)}
                          >
                            Cancel
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {companyMembers.length === 0 ? (
                      <p className="text-center py-4 text-slate-500">No members found</p>
                    ) : (
                      <div className="border rounded-md divide-y">
                        {companyMembers.map((member) => (
                          <div
                            key={member.id}
                            className="p-3 flex items-center justify-between"
                          >
                            <div className="flex items-center">
                              <Avatar className="h-8 w-8 mr-3">
                                {member.avatar_url ? (
                                  <AvatarImage src={member.avatar_url} alt={member.full_name || member.email || ''} />
                                ) : null}
                                <AvatarFallback>
                                  {getUserInitials(member.full_name || '', member.email || '')}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                {member.full_name && <p className="font-medium">{member.full_name}</p>}
                                <p className="text-sm text-slate-500">{member.email}</p>
                                <div className="flex items-center mt-1">
                                  <span className="text-xs px-2 py-0.5 bg-slate-100 dark:bg-slate-800 rounded-full">
                                    {member.role_type === 'admin' ? 'Admin' :
                                     member.role_type === 'superadmin' ? 'Superadmin' :
                                     'User'}
                                  </span>
                                  {member.user_id === user?.id && (
                                    <span className="text-xs ml-2 px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                                      You
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {/* Don't allow changing your own role or removing yourself */}
                              {member.user_id !== user?.id && (
                                <>
                                  {/* Role selector */}
                                  <RoleSelector
                                    userId={member.user_id}
                                    companyId={member.company_id}
                                    currentRole={member.role_type}
                                    disabled={!userIsSuperadmin && member.role_type === 'superadmin'}
                                  />

                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      if (id) {
                                        removeMember.mutate({ userId: member.user_id, companyId: id });
                                      }
                                    }}
                                    disabled={isLoading || removeMember.isPending}
                                  >
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {userIsSuperadmin && (
              <TabsContent value="monitors">
                <CompanyMonitorAssignment />
              </TabsContent>
            )}
          </Tabs>
        </div>
      </div>
    </AppLayout>
  );
};

export default CompanySettings;
