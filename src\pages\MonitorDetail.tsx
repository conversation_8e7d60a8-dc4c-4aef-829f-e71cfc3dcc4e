
import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Settings, Play, Pause, Trash2, CheckCircle, XCircle, Clock, ExternalLink, RefreshCw, AlertTriangle } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { toast } from "@/components/ui/use-toast";
import { useMonitors } from "@/hooks/use-monitors";
import { useMonitorHistory } from "@/hooks/use-monitor-history";
import { useMonitorStatusUpdate } from "@/hooks/use-monitor-status-update";
import { supabase } from "@/integrations/supabase/client";
import { useCompany } from "@/contexts/CompanyContext";
import AppLayout from "@/components/AppLayout";
import DocumentTitle from "@/components/DocumentTitle";
import UnifiedHeader from "@/components/UnifiedHeader";
import { useCompanyRoles } from "@/hooks/use-company-roles";
import { DegradedThresholds } from "@/types/monitor";
import { format, formatDistanceToNow } from "date-fns";
import { formatChartDate, formatTooltipDateTime, formatDateTime, formatDate, formatTime } from "@/utils/dateFormat";
import MonitorDebugTab from "@/components/MonitorDebugTab";
import { Skeleton } from "@/components/ui/skeleton";
import ManualCheckButton from "@/components/ManualCheckButton";
import { useQueryClient } from "@tanstack/react-query";



// Define interfaces for our data types
interface MonitorHistory {
  id: string;
  monitor_id: string;
  status: boolean | string;
  response_time: number | null;
  error_message: string | null;
  timestamp: string;
  status_code?: number;
}

interface Incident {
  id: string;
  date: string;
  time: string;
  duration: string;
  reason: string;
  status: string;
}

const MonitorDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAdmin, currentCompany } = useCompany();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin = false } = useGlobalSuperadminQuery();
  const { useGetMonitorQuery, useSoftDeleteMonitorMutation, useToggleMonitorStatusMutation } = useMonitors();
  const { getCompanyHistoryRetention } = useMonitorHistory();
  const { getMonitorStatus, hasStatusUpdate, clearStatusUpdate } = useMonitorStatusUpdate();
  const queryClient = useQueryClient();

  // Fetch monitor details using React Query
  const { data: monitor, isLoading: loading, error } = useGetMonitorQuery(id || '');

  // Get the latest status from the status update store
  const latestStatus = id ? getMonitorStatus(id) : null;

  // Function to get the current status (prioritizes latest status from store)
  const getCurrentStatus = () => {
    if (latestStatus) {
      return latestStatus.status;
    }
    return monitor?.status;
  };

  // Function to get the current response time (prioritizes latest from store)
  const getCurrentResponseTime = () => {
    if (latestStatus && latestStatus.response_time !== undefined) {
      return latestStatus.response_time;
    }
    return monitor?.last_response_time;
  };

  // Check if company has changed and navigate to dashboard if needed
  useEffect(() => {
    // Only check once when the component mounts
    const companyChanged = localStorage.getItem('company_changed');
    if (companyChanged === 'true') {
      console.log('Company changed, navigating to dashboard');
      localStorage.removeItem('company_changed');
      navigate('/dashboard');
    }
  }, []);

  // Get mutations for actions
  const deleteMonitorMutation = useSoftDeleteMonitorMutation();
  const toggleMonitorStatusMutation = useToggleMonitorStatusMutation();

  // State for monitor history
  const [history, setHistory] = useState<MonitorHistory[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(true);

  // State for uptime stats
  const [uptimeStats, setUptimeStats] = useState({
    uptime24h: 0,
    uptime7d: 0,
    avgResponseTime: 0
  });

  // State for chart data
  const [chartData, setChartData] = useState<any[]>([]);

  // State for degraded settings
  const [degradedSettings, setDegradedSettings] = useState<DegradedThresholds | null>(null);
  const [globalDegradedSettings, setGlobalDegradedSettings] = useState<DegradedThresholds | null>(null);
  const [loadingDegradedSettings, setLoadingDegradedSettings] = useState(true);

  // State for history retention period
  const [historyRetentionDays, setHistoryRetentionDays] = useState<number>(7);
  const [loadingRetentionPeriod, setLoadingRetentionPeriod] = useState(true);

  // Fetch monitor history
  const fetchHistory = async () => {
    if (!id) return;

    setLoadingHistory(true);
    try {
      // Calculate date range based on retention period
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - historyRetentionDays);

      const { data, error } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', id)
        .gte('timestamp', startDate.toISOString())
        .lte('timestamp', endDate.toISOString())
        .order('timestamp', { ascending: false })
        .limit(1000); // Increased limit to get more historical data

      if (error) throw error;

      setHistory(data || []);

      // Group data by date for better date labeling
      const dataByDate = {};
      if (data) {
        const reversedData = [...data].reverse(); // Reverse to show oldest first

        // Group data points by date
        reversedData.forEach((item: MonitorHistory) => {
          const date = new Date(item.timestamp);
          const dateKey = date.toDateString();

          if (!dataByDate[dateKey]) {
            dataByDate[dateKey] = [];
          }

          dataByDate[dateKey].push(item);
        });
      }

      // Prepare chart data with clear date indicators
      const chartData = [];

      // Process each date group
      Object.keys(dataByDate).forEach((dateKey, dateIndex) => {
        const items = dataByDate[dateKey];
        const firstItemDate = new Date(items[0].timestamp);

        // Add a date marker point at the beginning of each day
        const dateMarker = {
          time: formatDate(firstItemDate),
          value: items[0].response_time || 0,
          date: firstItemDate,
          showLabel: true, // Always show date labels
          isDateMarker: true,
          dateKey: dateKey
        };
        chartData.push(dateMarker);

        // Add the rest of the points for this date
        items.forEach((item: MonitorHistory) => {
          const itemDate = new Date(item.timestamp);
          const showTime = historyRetentionDays <= 1;

          chartData.push({
            time: formatChartDate(itemDate, showTime),
            value: item.response_time || 0,
            date: itemDate,
            showLabel: false, // Don't show labels for regular points
            dateKey: dateKey
          });
        });
      });

      setChartData(chartData);

      // Calculate uptime stats
      calculateUptimeStats(data || []);
    } catch (err) {
      console.error('Error fetching monitor history:', err);
      toast({
        title: 'Error',
        description: 'Failed to fetch monitor history',
        variant: 'destructive',
      });
    } finally {
      setLoadingHistory(false);
    }
  };

  // Calculate uptime statistics
  const calculateUptimeStats = (historyData: MonitorHistory[]) => {
    if (!historyData.length) {
      setUptimeStats({
        uptime24h: 0,
        uptime7d: 0,
        avgResponseTime: 0
      });
      return;
    }

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const history24h = historyData.filter(h => new Date(h.timestamp) >= oneDayAgo);
    const history7d = historyData.filter(h => new Date(h.timestamp) >= sevenDaysAgo);

    const uptime24h = history24h.length ?
      (history24h.filter(h => {
        return typeof h.status === 'string' ?
          h.status === 'up' :
          h.status === true || h.status === 1 || h.status === 'true' || h.status === '1';
      }).length / history24h.length) * 100 : 0;

    const uptime7d = history7d.length ?
      (history7d.filter(h => {
        return typeof h.status === 'string' ?
          h.status === 'up' :
          h.status === true || h.status === 1 || h.status === 'true' || h.status === '1';
      }).length / history7d.length) * 100 : 0;

    const responseTimes = historyData
      .filter(h => h.response_time !== null)
      .map(h => h.response_time as number);

    const avgResponseTime = responseTimes.length ?
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;

    setUptimeStats({
      uptime24h,
      uptime7d,
      avgResponseTime
    });
  };

  // Handle toggle monitor status
  const handleToggleStatus = () => {
    if (!monitor) return;

    toggleMonitorStatusMutation.mutate({ id: monitor.id, active: !monitor.active });
  };

  // Handle delete monitor
  const handleDelete = () => {
    if (!monitor) return;

    if (window.confirm(`Are you sure you want to delete the monitor "${monitor.name}"?`)) {
      deleteMonitorMutation.mutate(monitor.id, {
        onSuccess: () => {
          // Monitor deleted successfully - no toast needed
          // Navigate to dashboard with the current company to prevent company switching
          if (currentCompany) {
            console.log(`Successfully deleted monitor. Navigating to dashboard with company: ${currentCompany.id}`);
            // Store the current company ID in localStorage to ensure it's selected after navigation
            localStorage.setItem('lastSelectedCompanyId', currentCompany.id);
            navigate('/dashboard');
          } else {
            navigate('/dashboard');
          }
        },
        onError: (error) => {
          console.error('Error deleting monitor:', error);
          toast({
            title: 'Error',
            description: 'Failed to delete monitor. Please try again.',
            variant: 'destructive',
          });
        }
      });
    }
  };

  // Fetch history retention period
  const fetchHistoryRetentionPeriod = async () => {
    if (!currentCompany) return;

    setLoadingRetentionPeriod(true);
    try {
      const retentionDays = await getCompanyHistoryRetention(currentCompany.id);
      setHistoryRetentionDays(retentionDays);
    } catch (err) {
      console.error('Error fetching history retention period:', err);
      setHistoryRetentionDays(7); // Default to 7 days
    } finally {
      setLoadingRetentionPeriod(false);
    }
  };

  // Fetch degraded settings
  const fetchDegradedSettings = async () => {
    if (!id || !isSuperadmin) {
      setLoadingDegradedSettings(false);
      return;
    }

    setLoadingDegradedSettings(true);
    try {
      // Fetch global settings
      const { data: globalData, error: globalError } = await supabase
        .from('degraded_settings')
        .select('*')
        .limit(1)
        .single();

      if (globalError && globalError.code !== 'PGRST116') {
        console.error('Error fetching global degraded settings:', globalError);
      } else if (globalData) {
        setGlobalDegradedSettings({
          response_time: globalData.response_time,
          error_rate: globalData.error_rate,
          status_codes: globalData.status_codes || [],
          consecutive_failures: globalData.consecutive_failures
        });
      }

      // Fetch monitor-specific settings using RPC function
      const { data: monitorData, error: monitorError } = await supabase
        .rpc('get_monitor_degraded_settings_rpc', { p_monitor_id: id });

      if (monitorError) {
        console.error('Error fetching monitor degraded settings:', monitorError);
      } else if (monitorData) {
        setDegradedSettings({
          response_time: monitorData.response_time,
          error_rate: monitorData.error_rate,
          status_codes: monitorData.status_codes || [],
          consecutive_failures: monitorData.consecutive_failures
        });
      }
    } catch (err) {
      console.error('Error fetching degraded settings:', err);
    } finally {
      setLoadingDegradedSettings(false);
    }
  };

  // Load history data when component mounts or ID changes
  useEffect(() => {
    if (id) {
      fetchHistoryRetentionPeriod();
      fetchDegradedSettings();
    }
  }, [id, isSuperadmin, currentCompany]);

  // Fetch history when retention period is loaded
  useEffect(() => {
    if (id && !loadingRetentionPeriod) {
      fetchHistory();
    }
  }, [id, loadingRetentionPeriod, historyRetentionDays]);

  // Listen for status updates from manual checks and refresh data
  useEffect(() => {
    if (id && hasStatusUpdate(id)) {
      console.log('Status update detected for monitor', id, 'refreshing data...');

      // Invalidate and refetch the monitor query to get fresh data
      queryClient.invalidateQueries({ queryKey: ['monitor', id] });

      // Refresh history to show the latest check
      fetchHistory();

      // Clear the status update flag after handling it
      setTimeout(() => {
        clearStatusUpdate(id);
      }, 1000); // Small delay to ensure UI updates are visible
    }
  }, [id, hasStatusUpdate(id), queryClient, fetchHistory, clearStatusUpdate]);

  // Create a header for the error state
  const errorHeader = (
    <UnifiedHeader
      title="Monitor Not Found"
      showCompanySelector={true}
      actions={
        <Button variant="outline" onClick={() => navigate('/dashboard')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      }
    />
  );

  // Handle error state
  if (error) {
    return (
      <>
        <DocumentTitle title="Error - Monitor Not Found" />
        <AppLayout header={errorHeader}>
          <div className="container mx-auto py-8 px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl text-red-500">Error</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Failed to load monitor details. The monitor may not exist or you don't have permission to view it.</p>
              </CardContent>
              <CardFooter>
                <Button onClick={() => navigate('/dashboard')}>Return to Dashboard</Button>
              </CardFooter>
            </Card>
          </div>
        </AppLayout>
      </>
    );
  }

  // Create a header similar to the Dashboard page
  const header = (
    <UnifiedHeader
      title="Monitor Details"
      showCompanySelector={true}
      actions={
        <>
          <ManualCheckButton monitorId={id} />
        </>
      }
    />
  );

  return (
    <>
      <DocumentTitle title={monitor ? `${monitor.name} - Monitor Details` : 'Monitor Details'} />
      <AppLayout header={header}>
      <div className="container mx-auto py-6 px-4">

        {loading ? (
          <>
            <div className="flex flex-col lg:flex-row justify-between items-start gap-6 mb-6">
              <div>
                <Skeleton className="h-10 w-48 mb-2" />
                <Skeleton className="h-5 w-64" />
              </div>
              <div className="flex space-x-2">
                <Skeleton className="h-9 w-24" />
                <Skeleton className="h-9 w-24" />
                <Skeleton className="h-9 w-24" />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i}>
                  <CardHeader className="pb-2">
                    <Skeleton className="h-4 w-24" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-6 w-32" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </>
        ) : monitor ? (
          <>
            <div className="flex flex-col lg:flex-row justify-between items-start gap-6 mb-6">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <h1 className="text-3xl font-bold">{monitor.name}</h1>
                  <Badge variant={monitor.active ? 'default' : 'secondary'}>
                    {monitor.active ? 'Active' : 'Paused'}
                  </Badge>
                  <Badge
                    className={
                      !monitor.active ? 'bg-slate-200 hover:bg-slate-300 text-slate-800' :
                      (() => {
                        const currentStatus = getCurrentStatus();
                        if (typeof currentStatus === 'string') {
                          return currentStatus.toLowerCase() === 'up' ? 'bg-green-100 hover:bg-green-200 text-green-800' :
                                 currentStatus.toLowerCase() === 'degraded' ? 'bg-amber-100 hover:bg-amber-200 text-amber-800' :
                                 'bg-red-100 hover:bg-red-200 text-red-800';
                        }
                        return currentStatus === true || currentStatus === 1 ?
                               'bg-green-100 hover:bg-green-200 text-green-800' :
                               'bg-red-100 hover:bg-red-200 text-red-800';
                      })()
                    }
                  >
                    {!monitor.active ? 'PAUSED' :
                     (() => {
                       const currentStatus = getCurrentStatus();
                       if (typeof currentStatus === 'string') {
                         return currentStatus.toLowerCase() === 'up' ? 'UP' :
                                currentStatus.toLowerCase() === 'degraded' ? 'DEGRADED' :
                                'DOWN';
                       }
                       return currentStatus === true || currentStatus === 1 ? 'UP' : 'DOWN';
                     })()}
                  </Badge>
                  {isSuperadmin && degradedSettings && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="px-2 py-0 h-auto"
                      onClick={() => navigate(`/edit-monitor/${id}?focus=degraded`)}
                    >
                      <Badge
                        variant="outline"
                        className="text-blue-600 border-blue-600 dark:text-blue-400 dark:border-blue-400 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors flex items-center"
                      >
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Custom Degraded Settings
                      </Badge>
                    </Button>
                  )}
                </div>
                <p className="text-slate-600 dark:text-slate-400 flex items-center">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  <a href={monitor.target} target="_blank" rel="noopener noreferrer" className="hover:underline">
                    {monitor.target}
                  </a>
                </p>
              </div>
              <div className="flex space-x-2">
                <ManualCheckButton monitorId={id} />
                {isAdmin && (
                  <>
                    <Button variant="outline" size="sm" onClick={() => navigate(`/edit-monitor/${id}`)}>
                      <Settings className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleToggleStatus}>
                      {monitor.active ? (
                        <>
                          <Pause className="h-4 w-4 mr-2" />
                          Pause
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Activate
                        </>
                      )}
                    </Button>
                    <Button variant="destructive" size="sm" onClick={handleDelete}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <Card className="h-[80px] flex flex-col">
                <div className="flex items-center px-4 py-2">
                  <div className="flex-1">
                    <h3 className="text-sm text-slate-500 dark:text-slate-400">Status</h3>
                    <div className="flex items-center mt-1">
                      {/* Determine status based on active flag and current status */}
                      {!monitor.active ? (
                        <>
                          <Clock className="h-4 w-4 text-slate-500 mr-1" />
                          <span className="font-medium text-sm">Paused</span>
                        </>
                      ) : (() => {
                        const currentStatus = getCurrentStatus();
                        if (typeof currentStatus === 'string') {
                          return currentStatus.toLowerCase() === 'up' ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                              <span className="font-medium text-sm">Up</span>
                            </>
                          ) : currentStatus.toLowerCase() === 'degraded' ? (
                            <>
                              <AlertTriangle className="h-4 w-4 text-amber-500 mr-1" />
                              <span className="font-medium text-sm">Degraded</span>
                            </>
                          ) : (
                            <>
                              <XCircle className="h-4 w-4 text-red-500 mr-1" />
                              <span className="font-medium text-sm">Down</span>
                            </>
                          );
                        }
                        return currentStatus === true || currentStatus === 1 ? (
                          <>
                            <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                            <span className="font-medium text-sm">Up</span>
                          </>
                        ) : (
                          <>
                            <XCircle className="h-4 w-4 text-red-500 mr-1" />
                            <span className="font-medium text-sm">Down</span>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              </Card>
              <Card className="h-[80px] flex flex-col">
                <div className="flex items-center px-4 py-2">
                  <div className="flex-1">
                    <h3 className="text-sm text-slate-500 dark:text-slate-400">Uptime (24h)</h3>
                    <div className="font-medium text-sm mt-1">{uptimeStats.uptime24h.toFixed(2)}%</div>
                  </div>
                </div>
              </Card>
              <Card className="h-[80px] flex flex-col">
                <div className="flex items-center px-4 py-2">
                  <div className="flex-1">
                    <h3 className="text-sm text-slate-500 dark:text-slate-400">Last Response</h3>
                    <div className="font-medium text-sm mt-1">
                      {(() => {
                        const currentResponseTime = getCurrentResponseTime();
                        return currentResponseTime ? `${currentResponseTime} ms` : 'N/A';
                      })()}
                    </div>
                  </div>
                </div>
              </Card>
              <Card className="h-[80px] flex flex-col">
                <div className="flex items-center px-4 py-2">
                  <div className="flex-1">
                    <h3 className="text-sm text-slate-500 dark:text-slate-400">Last Checked</h3>
                    <div className="flex items-center mt-1">
                      <Clock className="h-4 w-4 text-slate-500 mr-1" />
                      <span className="font-medium text-sm">
                        {(() => {
                          // Use latest status timestamp if available, otherwise use history
                          const latestTimestamp = latestStatus?.timestamp;
                          const historyTimestamp = history.length > 0 ? history[0]?.timestamp : null;

                          const timestampToUse = latestTimestamp || historyTimestamp;

                          if (!timestampToUse) return 'Never';

                          const checkDate = new Date(timestampToUse);
                          const now = new Date();
                          const diffMs = now.getTime() - checkDate.getTime();
                          const diffMins = Math.floor(diffMs / 60000);

                          // Show more precise time for recent checks
                          if (diffMins < 1) return 'Just now';
                          if (diffMins === 1) return '1 minute ago';
                          if (diffMins < 60) return `${diffMins} minutes ago`;

                          // For older checks, show the actual time
                          return formatDateTime(checkDate);
                        })()}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            <Tabs defaultValue="overview" className="w-full">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
                {isSuperadmin && <TabsTrigger value="debug">Debug</TabsTrigger>}
              </TabsList>
              <TabsContent value="overview">
                <Card>
                  <CardHeader>
                    <CardTitle>Response Time</CardTitle>
                    <CardDescription>
                      Response time history (last {historyRetentionDays} days)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {loadingHistory ? (
                      <div className="h-[300px] w-full flex items-center justify-center">
                        <Skeleton className="h-[250px] w-full" />
                      </div>
                    ) : chartData.length > 0 ? (
                      <div className="h-[350px] w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={chartData}
                            margin={{
                              top: 5,
                              right: 30,
                              left: 20,
                              bottom: 50, // Increased to accommodate tilted labels
                            }}
                          >
                            {/* Removed CartesianGrid to eliminate dots behind the graph */}
                            <XAxis
                              dataKey="time"
                              tick={{ fontSize: 10 }}
                              tickFormatter={(value, index) => {
                                // Only show labels for date marker points
                                const dataPoint = chartData[index];
                                return dataPoint && dataPoint.isDateMarker ? value : '';
                              }}
                              angle={-45}
                              textAnchor="end"
                              height={60}
                              interval={0} // Show all ticks to ensure date labels appear
                            />
                            <YAxis unit="ms" />
                            <Tooltip
                              formatter={(value) => [`${value} ms`, 'Response Time']}
                              labelFormatter={(label, payload) => {
                                if (payload && payload[0] && payload[0].payload.date) {
                                  return formatTooltipDateTime(payload[0].payload.date);
                                }
                                return `Time: ${label}`;
                              }}
                              contentStyle={{ fontSize: '12px' }}
                            />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="#3b82f6"
                              strokeWidth={2}
                              dot={false} // Remove circles from data points
                              activeDot={{ r: 6 }} // Keep active dot for hover state
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="h-[300px] w-full flex items-center justify-center">
                        <p className="text-slate-500 dark:text-slate-400">No data available yet</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Check History</CardTitle>
                    <CardDescription>
                      Check history for the last {historyRetentionDays} days
                      {loadingHistory ? null : (
                        <span className="block text-xs mt-1">
                          Showing {history.length} checks
                        </span>
                      )}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {loadingHistory ? (
                      <div className="space-y-4">
                        {[1, 2, 3, 4, 5].map((i) => (
                          <div key={i} className="flex items-center space-x-4">
                            <Skeleton className="h-8 w-8 rounded-full" />
                            <div className="space-y-2 flex-1">
                              <Skeleton className="h-4 w-1/3" />
                              <Skeleton className="h-4 w-1/2" />
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : history.length === 0 ? (
                      <div className="text-center py-8">
                        <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">No History Yet</h3>
                        <p className="text-muted-foreground">
                          This monitor doesn't have any check history yet. Checks will appear here as they are performed.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2">
                        {history.map((item) => (
                          <div key={item.id} className="flex items-start border-b pb-4 last:border-0 last:pb-0">
                            <div className="mr-4 mt-1">
                              {typeof item.status === 'string' ? (
                                item.status === 'up' ? (
                                  <CheckCircle className="h-5 w-5 text-green-500" />
                                ) : item.status === 'degraded' ? (
                                  <AlertTriangle className="h-5 w-5 text-amber-500" />
                                ) : (
                                  <XCircle className="h-5 w-5 text-red-500" />
                                )
                              ) : item.status === true || item.status === 1 || item.status === 'true' || item.status === '1' ? (
                                <CheckCircle className="h-5 w-5 text-green-500" />
                              ) : (
                                <XCircle className="h-5 w-5 text-red-500" />
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium">
                                  {typeof item.status === 'string' ? (
                                    item.status === 'up' ? 'UP' :
                                    item.status === 'degraded' ? 'DEGRADED' :
                                    'DOWN'
                                  ) : item.status === true || item.status === 1 || item.status === 'true' || item.status === '1' ? 'UP' : 'DOWN'}
                                </h4>
                                <span className="text-sm text-muted-foreground">
                                  {formatTooltipDateTime(new Date(item.timestamp))}
                                </span>
                              </div>
                              {item.response_time && (
                                <p className="text-sm">
                                  Response time: {item.response_time} ms
                                </p>
                              )}
                              {item.error_message && (
                                <p className="text-sm text-red-500 mt-1">
                                  {item.error_message}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                  <CardFooter>
                    <Button
                      variant="outline"
                      onClick={fetchHistory}
                      disabled={loadingHistory}
                      className="w-full"
                    >
                      <RefreshCw className={`h-4 w-4 mr-2 ${loadingHistory ? 'animate-spin' : ''}`} />
                      {loadingHistory ? 'Loading...' : 'Refresh History'}
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="settings">
                <Card>
                  <CardHeader>
                    <CardTitle>Monitor Settings</CardTitle>
                    <CardDescription>
                      Current configuration for this monitor
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="font-medium mb-2">Basic Information</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-slate-600 dark:text-slate-400">Monitor Type:</span>
                            <span className="font-medium">{monitor.type.toUpperCase()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-600 dark:text-slate-400">Created:</span>
                            <span className="font-medium">{formatDateTime(new Date(monitor.created_at))}</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="font-medium mb-2">Monitor Configuration</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-slate-600 dark:text-slate-400">Check Interval:</span>
                            <span className="font-medium">{monitor.interval} minutes</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-600 dark:text-slate-400">Timeout:</span>
                            <span className="font-medium">{monitor.timeout} seconds</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {isSuperadmin && (
                      <div className="mt-6">
                        <h3 className="font-medium mb-2 flex items-center">
                          <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
                          Degraded Service Settings
                          {degradedSettings && (
                            <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              Custom Settings Active
                            </Badge>
                          )}
                        </h3>

                        {loadingDegradedSettings ? (
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                          </div>
                        ) : degradedSettings ? (
                          <div className="space-y-4 border-2 border-blue-200 dark:border-blue-800 rounded-md p-4 relative bg-blue-50/50 dark:bg-blue-900/20">
                            {isSuperadmin && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="absolute top-2 right-2"
                                onClick={() => navigate(`/edit-monitor/${id}?focus=degraded`)}
                              >
                                <Settings className="h-4 w-4 mr-1" />
                                Edit Degraded Settings
                              </Button>
                            )}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <h4 className="text-sm font-medium">Response Time Threshold</h4>
                                <div className="flex justify-between">
                                  <span className="text-slate-600 dark:text-slate-400">Custom Setting:</span>
                                  <span className="font-medium">{degradedSettings.response_time} ms</span>
                                </div>
                                {globalDegradedSettings && (
                                  <div className="flex justify-between text-xs text-slate-500">
                                    <span>Global Default:</span>
                                    <span>{globalDegradedSettings.response_time} ms</span>
                                  </div>
                                )}
                                <p className="text-xs text-slate-500 mt-1">
                                  Services with response times above this threshold will be considered degraded.
                                </p>
                              </div>

                              <div className="space-y-2">
                                <h4 className="text-sm font-medium">Error Rate Threshold</h4>
                                <div className="flex justify-between">
                                  <span className="text-slate-600 dark:text-slate-400">Custom Setting:</span>
                                  <span className="font-medium">{degradedSettings.error_rate}%</span>
                                </div>
                                {globalDegradedSettings && (
                                  <div className="flex justify-between text-xs text-slate-500">
                                    <span>Global Default:</span>
                                    <span>{globalDegradedSettings.error_rate}%</span>
                                  </div>
                                )}
                                <p className="text-xs text-slate-500 mt-1">
                                  Services with error rates above this percentage will be considered degraded.
                                </p>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <h4 className="text-sm font-medium">Consecutive Failures</h4>
                                <div className="flex justify-between">
                                  <span className="text-slate-600 dark:text-slate-400">Custom Setting:</span>
                                  <span className="font-medium">{degradedSettings.consecutive_failures}</span>
                                </div>
                                {globalDegradedSettings && (
                                  <div className="flex justify-between text-xs text-slate-500">
                                    <span>Global Default:</span>
                                    <span>{globalDegradedSettings.consecutive_failures}</span>
                                  </div>
                                )}
                                <p className="text-xs text-slate-500 mt-1">
                                  Number of consecutive partial failures before a service is considered degraded.
                                </p>
                              </div>

                              <div className="space-y-2">
                                <h4 className="text-sm font-medium">Status Codes</h4>
                                <div className="flex justify-between">
                                  <span className="text-slate-600 dark:text-slate-400">Custom Setting:</span>
                                  <span className="font-medium">
                                    {degradedSettings.status_codes?.length > 0
                                      ? degradedSettings.status_codes.join(', ')
                                      : 'None'}
                                  </span>
                                </div>
                                {globalDegradedSettings && (
                                  <div className="flex justify-between text-xs text-slate-500">
                                    <span>Global Default:</span>
                                    <span>
                                      {globalDegradedSettings.status_codes?.length > 0
                                        ? globalDegradedSettings.status_codes.join(', ')
                                        : 'None'}
                                    </span>
                                  </div>
                                )}
                                <p className="text-xs text-slate-500 mt-1">
                                  HTTP status codes that indicate a degraded service rather than a complete outage.
                                </p>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="text-slate-600 dark:text-slate-400 border rounded-md p-4">
                            <p>This monitor uses global degraded settings. No custom settings are configured.</p>
                            {globalDegradedSettings && (
                              <div className="mt-2 space-y-2">
                                <div className="grid grid-cols-2 gap-2 text-sm">
                                  <div>Response Time Threshold:</div>
                                  <div>{globalDegradedSettings.response_time} ms</div>

                                  <div>Error Rate Threshold:</div>
                                  <div>{globalDegradedSettings.error_rate}%</div>

                                  <div>Consecutive Failures:</div>
                                  <div>{globalDegradedSettings.consecutive_failures}</div>

                                  <div>Status Codes:</div>
                                  <div>
                                    {globalDegradedSettings.status_codes?.length > 0
                                      ? globalDegradedSettings.status_codes.join(', ')
                                      : 'None'}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                    {isAdmin && (
                      <div className="mt-6">
                        <Button onClick={() => navigate(`/edit-monitor/${id}`)}>
                          <Settings className="h-4 w-4 mr-2" />
                          Edit Configuration
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {isSuperadmin && (
                <TabsContent value="debug">
                  <MonitorDebugTab
                    monitorId={id || ''}
                    monitorName={monitor?.name || ''}
                    monitorUrl={monitor?.target || ''}
                  />
                </TabsContent>
              )}
            </Tabs>
          </>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Monitor Not Found</CardTitle>
            </CardHeader>
            <CardContent>
              <p>The requested monitor could not be found or you don't have permission to view it.</p>
            </CardContent>
            <CardFooter>
              <Button onClick={() => navigate('/dashboard')}>Return to Dashboard</Button>
            </CardFooter>
          </Card>
        )}
      </div>
      </AppLayout>
    </>
  );
};

export default MonitorDetail;
