/**
 * Backend Configuration Utility
 * 
 * This utility helps manage switching between local and external backends.
 * It provides information about the current backend configuration and
 * validation of environment variables.
 */

export type BackendMode = 'local' | 'external';

export interface BackendConfig {
  mode: BackendMode;
  supabaseUrl: string;
  supabaseKey: string;
  isValid: boolean;
  errors: string[];
}

/**
 * Get the current backend configuration
 */
export function getBackendConfig(): BackendConfig {
  const mode = (import.meta.env.VITE_BACKEND_MODE || 'local') as BackendMode;
  const errors: string[] = [];
  
  let supabaseUrl: string;
  let supabaseKey: string;
  
  if (mode === 'external') {
    supabaseUrl = import.meta.env.VITE_EXTERNAL_SUPABASE_URL || '';
    supabaseKey = import.meta.env.VITE_EXTERNAL_SUPABASE_ANON_KEY || '';
    
    if (!supabaseUrl) {
      errors.push('VITE_EXTERNAL_SUPABASE_URL is not set');
    }
    if (!supabaseKey) {
      errors.push('VITE_EXTERNAL_SUPABASE_ANON_KEY is not set');
    }
  } else {
    supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
    supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
    
    if (!supabaseUrl) {
      errors.push('VITE_SUPABASE_URL is not set');
    }
    if (!supabaseKey) {
      errors.push('VITE_SUPABASE_ANON_KEY is not set');
    }
  }
  
  return {
    mode,
    supabaseUrl,
    supabaseKey,
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate the current backend configuration
 */
export function validateBackendConfig(): { isValid: boolean; errors: string[] } {
  const config = getBackendConfig();
  return {
    isValid: config.isValid,
    errors: config.errors
  };
}

/**
 * Get a human-readable description of the current backend
 */
export function getBackendDescription(): string {
  const config = getBackendConfig();
  
  if (!config.isValid) {
    return `❌ Invalid ${config.mode} backend configuration`;
  }
  
  const emoji = config.mode === 'external' ? '🌐' : '🏠';
  const label = config.mode === 'external' ? 'External' : 'Local';
  
  return `${emoji} ${label} Backend: ${config.supabaseUrl}`;
}

/**
 * Check if we're using the external backend
 */
export function isExternalBackend(): boolean {
  return getBackendConfig().mode === 'external';
}

/**
 * Check if we're using the local backend
 */
export function isLocalBackend(): boolean {
  return getBackendConfig().mode === 'local';
}
