/**
 * VUM Backend Service Configuration Utility
 *
 * This utility helps manage the configuration for the VUM Backend monitoring service.
 * The VUM Backend service can run either locally (for development) or on an external server.
 * Supabase is always the same cloud instance regardless of where the VUM Backend runs.
 */

export type BackendServiceMode = 'local' | 'external';

export interface BackendServiceConfig {
  mode: BackendServiceMode;
  backendUrl: string;
  supabaseUrl: string;
  supabaseKey: string;
  isValid: boolean;
  errors: string[];
}

/**
 * Get the current VUM Backend service configuration
 */
export function getBackendServiceConfig(): BackendServiceConfig {
  const mode = (import.meta.env.VITE_BACKEND_SERVICE_MODE || 'local') as BackendServiceMode;
  const errors: string[] = [];

  // Supabase is always the same
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

  if (!supabaseUrl) {
    errors.push('VITE_SUPABASE_URL is not set');
  }
  if (!supabaseKey) {
    errors.push('VITE_SUPABASE_ANON_KEY is not set');
  }

  // VUM Backend service URL depends on mode
  let backendUrl: string;

  if (mode === 'external') {
    backendUrl = import.meta.env.VITE_EXTERNAL_BACKEND_URL || '';
    if (!backendUrl) {
      errors.push('VITE_EXTERNAL_BACKEND_URL is not set');
    }
  } else {
    backendUrl = import.meta.env.VITE_LOCAL_BACKEND_URL || 'http://localhost:3001';
  }

  return {
    mode,
    backendUrl,
    supabaseUrl,
    supabaseKey,
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate the current VUM Backend service configuration
 */
export function validateBackendServiceConfig(): { isValid: boolean; errors: string[] } {
  const config = getBackendServiceConfig();
  return {
    isValid: config.isValid,
    errors: config.errors
  };
}

/**
 * Get a human-readable description of the current VUM Backend service
 */
export function getBackendServiceDescription(): string {
  const config = getBackendServiceConfig();

  if (!config.isValid) {
    return `❌ Invalid VUM Backend service configuration`;
  }

  const emoji = config.mode === 'external' ? '🌐' : '🏠';
  const label = config.mode === 'external' ? 'External' : 'Local';

  return `${emoji} ${label} VUM Backend: ${config.backendUrl}`;
}

/**
 * Check if we're using the external VUM Backend service
 */
export function isExternalBackendService(): boolean {
  return getBackendServiceConfig().mode === 'external';
}

/**
 * Check if we're using the local VUM Backend service
 */
export function isLocalBackendService(): boolean {
  return getBackendServiceConfig().mode === 'local';
}

/**
 * Get the current VUM Backend service URL
 */
export function getBackendServiceUrl(): string {
  return getBackendServiceConfig().backendUrl;
}
