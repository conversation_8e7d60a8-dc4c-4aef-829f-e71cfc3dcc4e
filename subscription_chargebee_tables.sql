-- Features table to store all possible features
CREATE TABLE subscription_features (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    key VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMPTZ,
    deleted_by UUID REFERENCES auth.users(id)
);

-- Subscription tiers table (linked to Chargebee plans)
CREATE TABLE subscription_tiers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    chargebee_plan_id VARCHAR(100) NOT NULL UNIQUE,
    is_custom BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMPTZ,
    deleted_by UUID REFERENCES auth.users(id)
);

-- Junction table for tier features and their limits
CREATE TABLE tier_features (
    tier_id UUID REFERENCES subscription_tiers(id),
    feature_id UUID REFERENCES subscription_features(id),
    value JSONB NOT NULL, -- Stores feature limits/settings as JSON
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    PRIMARY KEY (tier_id, feature_id)
);

-- Company subscriptions table (maps companies to Chargebee subscriptions)
CREATE TABLE company_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    company_id UUID NOT NULL REFERENCES companies(id),
    tier_id UUID NOT NULL REFERENCES subscription_tiers(id),
    chargebee_subscription_id VARCHAR(100) NOT NULL UNIQUE,
    chargebee_customer_id VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'cancelled', 'expired', 'trial')),
    custom_features JSONB, -- For custom tier overrides
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Function to check if a user is a superadmin
CREATE OR REPLACE FUNCTION is_superadmin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_roles 
        WHERE user_id = $1 
        AND role = 'superadmin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies
ALTER TABLE subscription_features ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_tiers ENABLE ROW LEVEL SECURITY;
ALTER TABLE tier_features ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_subscriptions ENABLE ROW LEVEL SECURITY;

-- Superadmin can do everything
CREATE POLICY superadmin_all_features ON subscription_features
    TO authenticated
    USING (is_superadmin(auth.uid()))
    WITH CHECK (is_superadmin(auth.uid()));

CREATE POLICY superadmin_all_tiers ON subscription_tiers
    TO authenticated
    USING (is_superadmin(auth.uid()))
    WITH CHECK (is_superadmin(auth.uid()));

CREATE POLICY superadmin_all_tier_features ON tier_features
    TO authenticated
    USING (is_superadmin(auth.uid()))
    WITH CHECK (is_superadmin(auth.uid()));

-- Company admins can view their subscription
CREATE POLICY view_company_subscription ON company_subscriptions
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_companies uc
            WHERE uc.user_id = auth.uid()
            AND uc.company_id = company_subscriptions.company_id
        )
    );

-- Everyone can view tiers (for pricing page)
CREATE POLICY view_tiers ON subscription_tiers
    FOR SELECT
    TO authenticated
    USING (NOT deleted AND NOT is_custom);

-- Everyone can view features
CREATE POLICY view_features ON subscription_features
    FOR SELECT
    TO authenticated
    USING (NOT deleted);

-- Everyone can view tier features
CREATE POLICY view_tier_features ON tier_features
    FOR SELECT
    TO authenticated
    USING (true);

-- Insert some default features
INSERT INTO subscription_features (name, description, key) VALUES
    ('Monitor Count', 'Number of monitors allowed', 'monitor_count'),
    ('Check Interval', 'Minimum check interval in seconds', 'check_interval'),
    ('Team Members', 'Number of team members allowed', 'team_members'),
    ('History Retention', 'Number of days to retain history', 'history_retention'),
    ('API Access', 'API access enabled', 'api_access'),
    ('Custom Domains', 'Custom domain support', 'custom_domains'),
    ('Status Pages', 'Number of status pages allowed', 'status_pages');

-- Insert default tiers with Chargebee plan IDs
INSERT INTO subscription_tiers (name, description, chargebee_plan_id) VALUES
    ('Free', 'Basic monitoring for small projects', 'vum-free-v1'),
    ('Pro', 'Advanced monitoring for growing businesses', 'vum-pro-v1'),
    ('Business', 'Enterprise-grade monitoring solution', 'vum-business-v1');

-- Set up tier features
INSERT INTO tier_features (tier_id, feature_id, value) 
SELECT 
    t.id,
    f.id,
    CASE 
        WHEN t.name = 'Free' THEN
            CASE f.key
                WHEN 'monitor_count' THEN '{"limit": 3}'::jsonb
                WHEN 'check_interval' THEN '{"limit": 300}'::jsonb
                WHEN 'team_members' THEN '{"limit": 1}'::jsonb
                WHEN 'history_retention' THEN '{"limit": 7}'::jsonb
                WHEN 'api_access' THEN '{"enabled": false}'::jsonb
                WHEN 'custom_domains' THEN '{"enabled": false}'::jsonb
                WHEN 'status_pages' THEN '{"limit": 1}'::jsonb
            END
        WHEN t.name = 'Pro' THEN
            CASE f.key
                WHEN 'monitor_count' THEN '{"limit": 20}'::jsonb
                WHEN 'check_interval' THEN '{"limit": 60}'::jsonb
                WHEN 'team_members' THEN '{"limit": 5}'::jsonb
                WHEN 'history_retention' THEN '{"limit": 30}'::jsonb
                WHEN 'api_access' THEN '{"enabled": true}'::jsonb
                WHEN 'custom_domains' THEN '{"enabled": false}'::jsonb
                WHEN 'status_pages' THEN '{"limit": 3}'::jsonb
            END
        WHEN t.name = 'Business' THEN
            CASE f.key
                WHEN 'monitor_count' THEN '{"limit": 100}'::jsonb
                WHEN 'check_interval' THEN '{"limit": 30}'::jsonb
                WHEN 'team_members' THEN '{"limit": 20}'::jsonb
                WHEN 'history_retention' THEN '{"limit": 90}'::jsonb
                WHEN 'api_access' THEN '{"enabled": true}'::jsonb
                WHEN 'custom_domains' THEN '{"enabled": true}'::jsonb
                WHEN 'status_pages' THEN '{"limit": 10}'::jsonb
            END
    END
FROM subscription_tiers t
CROSS JOIN subscription_features f;
