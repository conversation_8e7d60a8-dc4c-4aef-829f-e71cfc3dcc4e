// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { command } = await req.json()

    if (!command) {
      return new Response(
        JSON.stringify({ error: 'Command is required' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // For security, we'll only allow specific commands
    if (!command.startsWith('cd C:\\VUM\\monitor-service && npm run check-now')) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized command' }),
        { 
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // In a real implementation, you would execute the command here
    // But since we can't execute commands from an Edge Function,
    // we'll simulate the response

    // Simulate a successful check
    const results = []
    const monitorId = command.includes('--id=') 
      ? command.split('--id=')[1].trim()
      : null

    if (monitorId) {
      // Simulate checking a specific monitor
      results.push({
        monitor_id: monitorId,
        name: 'Monitor',
        status: true,
        response_time: 500,
        error_message: null
      })
    } else {
      // Simulate checking all monitors
      for (let i = 0; i < 5; i++) {
        results.push({
          monitor_id: `monitor-${i}`,
          name: `Monitor ${i}`,
          status: Math.random() > 0.2, // 80% chance of being up
          response_time: Math.floor(Math.random() * 1000),
          error_message: null
        })
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        checksRun: results.length,
        results
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
