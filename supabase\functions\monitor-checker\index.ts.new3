    const now = Date.now();
    const checksToRun = [];
    
    // Determine which monitors need to be checked
    for (const monitor of monitors){
      // If a specific monitor ID was provided, we'll check it regardless of the interval
      if (specificMonitorId) {
        checksToRun.push(monitor);
        continue;
      }
      
      const { data: lastCheck } = await supabase.from('monitor_history').select('timestamp').eq('monitor_id', monitor.id).order('timestamp', {
        ascending: false
      }).limit(1);
      
      // If no previous check exists, check it now
      if (!lastCheck || lastCheck.length === 0) {
        checksToRun.push(monitor);
        continue;
      }
      
      const lastCheckTime = new Date(lastCheck[0].timestamp).getTime();
      const intervalMs = monitor.interval * 60 * 1000;
      const timeSinceLastCheck = now - lastCheckTime;
      
      console.log(`Monitor ${monitor.name}: interval=${monitor.interval}min, last check=${Math.floor(timeSinceLastCheck / 60000)}min ago`);
      
      // Check if it's time to run this monitor again
      // We add a small buffer (10 seconds) to account for processing time
      if (timeSinceLastCheck >= intervalMs - 10000) {
        checksToRun.push(monitor);
      }
    }
    
    console.log(`Running checks for ${checksToRun.length} monitors`);
    
    // Run checks with concurrency control
    const results = await runWithConcurrency(checksToRun, async (monitor)=>{
      console.log(`Checking monitor: ${monitor.name} (${monitor.target})`);
      const checkResult = await performCheck(monitor);
      console.log(`Check result for ${monitor.name}: ${checkResult.status ? 'UP' : 'DOWN'} (${checkResult.response_time}ms)`);
      
      // Save the check result
      const { error: insertError } = await supabase.from('monitor_history').insert(checkResult);
      
      if (insertError) {
        console.error(`Error saving check result: ${insertError.message}`);
      }
      
      // Update the monitor's status in the monitors table
      let monitorStatus = checkResult.status ? 'up' : 'down';

      // Get the effective degraded settings for this monitor using the database function
      const { data: effectiveSettings, error: settingsError } = await supabase
        .rpc('get_monitor_degraded_settings', { monitor_id: monitor.id });

      if (settingsError) {
        console.error(`Error getting degraded settings for monitor ${monitor.name}:`, settingsError);
      }

      // If the monitor is up but meets degraded criteria, mark it as degraded
      if (monitorStatus === 'up' && checkResult.response_time && effectiveSettings) {
        // Get the response time threshold from the effective settings
        const responseTimeThreshold = effectiveSettings.response_time || 1000;

        if (checkResult.response_time > responseTimeThreshold) {
          console.log(`Monitor ${monitor.name} response time (${checkResult.response_time}ms) exceeds threshold (${responseTimeThreshold}ms), marking as degraded`);
          monitorStatus = 'degraded';
        } else {
          console.log(`Monitor ${monitor.name} response time (${checkResult.response_time}ms) is within threshold (${responseTimeThreshold}ms), status is UP`);
        }
      }

      // Update the monitor status in the database
      const { error: updateError } = await supabase
        .from('monitors')
        .update({
          status: monitorStatus,
          last_check_time: checkResult.timestamp,
          last_response_time: checkResult.response_time
        })
        .eq('id', monitor.id);

      if (updateError) {
        console.error(`Error updating monitor status: ${updateError.message}`);
      }
      
      // Check if status changed and send notification if needed
      const statusChanged = await checkStatusChange(supabase, monitor, checkResult.status);
      if (statusChanged) {
        await sendNotification(supabase, monitor, checkResult.status);
      }
      
      return {
        monitor_id: monitor.id,
        name: monitor.name,
        status: checkResult.status,
        response_time: checkResult.response_time,
        error_message: checkResult.error_message
      };
    }, 5);
    
    return new Response(JSON.stringify({
      success: true,
      checksRun: results.length,
      results
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error in monitor checker:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
