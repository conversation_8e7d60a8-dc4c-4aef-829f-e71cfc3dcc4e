// Mock Supabase client helper
const createMockSupabaseClient = () => {
    const mockSupabase = {
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        rpc: jest.fn(),
        auth: {
            getUser: jest.fn()
        }
    };

    return mockSupabase;
};

// Mock user tokens
const mockTokens = {
    superadmin: 'mock-superadmin-token',
    admin: 'mock-admin-token',
    user: 'mock-user-token'
};

// Mock users
const mockUsers = {
    superadmin: { id: 'superadmin-id', role: 'superadmin' },
    admin: { id: 'admin-id', role: 'admin' },
    user: { id: 'user-id', role: 'user' }
};

module.exports = {
    createMockSupabaseClient,
    mockTokens,
    mockUsers
};
