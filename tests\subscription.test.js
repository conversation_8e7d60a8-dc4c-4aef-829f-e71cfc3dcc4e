const request = require('supertest');
const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const subscriptionRoutes = require('../routes/subscription');

// Mock Supabase client
jest.mock('@supabase/supabase-js');

const app = express();
app.use(express.json());
app.use('/subscription', subscriptionRoutes);

describe('Subscription API', () => {
    let mockSupabase;

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Mock Supabase client implementation
        mockSupabase = {
            from: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
            update: jest.fn().mockReturnThis(),
            delete: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockReturnThis(),
            order: jest.fn().mockReturnThis(),
            rpc: jest.fn()
        };

        createClient.mockReturnValue(mockSupabase);
    });

    describe('GET /subscription/tiers', () => {
        it('should return all subscription tiers with features', async () => {
            const mockTiers = [
                {
                    id: '1',
                    name: 'Free',
                    description: 'Free tier',
                    chargebee_plan_id: 'free-v1',
                    features: [
                        {
                            feature: {
                                key: 'monitor_count',
                                name: 'Monitor Count',
                                description: 'Number of monitors'
                            },
                            value: { limit: 3 }
                        }
                    ]
                }
            ];

            mockSupabase.select.mockResolvedValueOnce({ data: mockTiers, error: null });

            const response = await request(app)
                .get('/subscription/tiers')
                .expect(200);

            expect(response.body).toHaveLength(1);
            expect(response.body[0].features).toHaveProperty('monitor_count');
            expect(response.body[0].features.monitor_count.limit).toBe(3);
        });

        it('should handle errors when fetching tiers', async () => {
            mockSupabase.select.mockResolvedValueOnce({ 
                data: null, 
                error: new Error('Database error') 
            });

            await request(app)
                .get('/subscription/tiers')
                .expect(500)
                .expect({ error: 'Failed to fetch subscription tiers' });
        });
    });

    describe('GET /subscription/features (Superadmin)', () => {
        const mockSuperadminToken = 'mock-superadmin-token';
        const mockUser = { id: 'superadmin-id' };

        beforeEach(() => {
            // Mock auth middleware successful case
            mockSupabase.auth = {
                getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null })
            };
            mockSupabase.select.mockImplementation(() => ({
                eq: jest.fn().mockResolvedValue({ data: { role: 'superadmin' }, error: null })
            }));
        });

        it('should return all features for superadmin', async () => {
            const mockFeatures = [
                {
                    id: '1',
                    name: 'Monitor Count',
                    key: 'monitor_count',
                    description: 'Number of monitors allowed'
                }
            ];

            mockSupabase.select.mockResolvedValueOnce({ data: mockFeatures, error: null });

            const response = await request(app)
                .get('/subscription/features')
                .set('Authorization', `Bearer ${mockSuperadminToken}`)
                .expect(200);

            expect(response.body).toEqual(mockFeatures);
        });

        it('should reject non-superadmin users', async () => {
            mockSupabase.select.mockImplementation(() => ({
                eq: jest.fn().mockResolvedValue({ data: null, error: 'Not found' })
            }));

            await request(app)
                .get('/subscription/features')
                .set('Authorization', `Bearer ${mockSuperadminToken}`)
                .expect(403)
                .expect({ error: 'Not authorized' });
        });
    });

    describe('POST /subscription/features (Superadmin)', () => {
        const mockSuperadminToken = 'mock-superadmin-token';
        const mockUser = { id: 'superadmin-id' };

        beforeEach(() => {
            mockSupabase.auth = {
                getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null })
            };
            mockSupabase.select.mockImplementation(() => ({
                eq: jest.fn().mockResolvedValue({ data: { role: 'superadmin' }, error: null })
            }));
        });

        it('should create a new feature', async () => {
            const newFeature = {
                name: 'API Access',
                key: 'api_access',
                description: 'Access to API endpoints'
            };

            mockSupabase.insert.mockResolvedValueOnce({
                data: { id: '1', ...newFeature },
                error: null
            });

            const response = await request(app)
                .post('/subscription/features')
                .set('Authorization', `Bearer ${mockSuperadminToken}`)
                .send(newFeature)
                .expect(201);

            expect(response.body).toMatchObject(newFeature);
        });
    });

    describe('PUT /subscription/tiers/:id/features (Superadmin)', () => {
        const mockSuperadminToken = 'mock-superadmin-token';
        const mockUser = { id: 'superadmin-id' };

        beforeEach(() => {
            mockSupabase.auth = {
                getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null })
            };
            mockSupabase.select.mockImplementation(() => ({
                eq: jest.fn().mockResolvedValue({ data: { role: 'superadmin' }, error: null })
            }));
        });

        it('should update tier features successfully', async () => {
            const tierId = '1';
            const features = {
                'feature-1': { limit: 10 },
                'feature-2': { enabled: true }
            };

            // Mock successful transaction
            mockSupabase.rpc
                .mockResolvedValueOnce({ error: null }) // begin
                .mockResolvedValueOnce({ error: null }); // commit

            mockSupabase.delete.mockResolvedValueOnce({ error: null });
            mockSupabase.insert.mockResolvedValueOnce({ error: null });

            const response = await request(app)
                .put(`/subscription/tiers/${tierId}/features`)
                .set('Authorization', `Bearer ${mockSuperadminToken}`)
                .send({ features })
                .expect(200);

            expect(response.body).toEqual({ message: 'Tier features updated successfully' });
        });

        it('should handle transaction errors', async () => {
            const tierId = '1';
            const features = {
                'feature-1': { limit: 10 }
            };

            // Mock transaction error
            mockSupabase.rpc.mockResolvedValueOnce({ error: new Error('Transaction error') });

            await request(app)
                .put(`/subscription/tiers/${tierId}/features`)
                .set('Authorization', `Bearer ${mockSuperadminToken}`)
                .send({ features })
                .expect(500)
                .expect({ error: 'Failed to update tier features' });
        });
    });

    describe('GET /subscription/companies/:id/subscription', () => {
        const mockToken = 'mock-token';
        const mockUser = { id: 'user-id' };
        const companyId = 'company-1';

        beforeEach(() => {
            mockSupabase.auth = {
                getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null })
            };
        });

        it('should return company subscription for authorized user', async () => {
            const mockSubscription = {
                id: '1',
                company_id: companyId,
                tier: {
                    id: '2',
                    name: 'Pro',
                    features: [
                        {
                            feature: {
                                key: 'monitor_count',
                                name: 'Monitor Count'
                            },
                            value: { limit: 20 }
                        }
                    ]
                }
            };

            // Mock user company access
            mockSupabase.select.mockImplementationOnce(() => ({
                eq: jest.fn().mockImplementation(() => ({
                    eq: jest.fn().mockResolvedValue({ data: { role: 'admin' }, error: null })
                }))
            }));

            // Mock subscription data
            mockSupabase.select.mockImplementationOnce(() => ({
                eq: jest.fn().mockResolvedValue({ data: mockSubscription, error: null })
            }));

            const response = await request(app)
                .get(`/subscription/companies/${companyId}/subscription`)
                .set('Authorization', `Bearer ${mockToken}`)
                .expect(200);

            expect(response.body.tier.features).toHaveProperty('monitor_count');
            expect(response.body.tier.features.monitor_count.limit).toBe(20);
        });

        it('should reject unauthorized users', async () => {
            // Mock no company access
            mockSupabase.select.mockImplementationOnce(() => ({
                eq: jest.fn().mockImplementation(() => ({
                    eq: jest.fn().mockResolvedValue({ data: null, error: 'Not found' })
                }))
            }));

            await request(app)
                .get(`/subscription/companies/${companyId}/subscription`)
                .set('Authorization', `Bearer ${mockToken}`)
                .expect(403)
                .expect({ error: 'Not authorized to access this company' });
        });
    });
});
